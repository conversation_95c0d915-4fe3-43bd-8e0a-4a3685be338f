import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/notification_history_model.dart';
import 'package:healo/providers/notification_history_provider.dart';
import 'package:healo/common/utils/snackbar.dart';

class NotificationScreen extends ConsumerStatefulWidget {
  const NotificationScreen({super.key});

  @override
  ConsumerState<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends ConsumerState<NotificationScreen> with WidgetsBindingObserver {
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationHistoryProvider.notifier).fetchNotificationHistory();
      _startPeriodicRefresh();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// Start periodic refresh to sync with external notification actions
  void _startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted) {
        // Use silent refresh to avoid showing loading indicator repeatedly
        ref.read(notificationHistoryProvider.notifier).silentRefresh();
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Refresh notification history when app comes to foreground
    if (state == AppLifecycleState.resumed) {
      ref.read(notificationHistoryProvider.notifier).silentRefresh();
      _startPeriodicRefresh();
    } else if (state == AppLifecycleState.paused) {
      // Stop periodic refresh when app goes to background to save battery
      _refreshTimer?.cancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    final notificationState = ref.watch(notificationHistoryProvider);
    final notificationsByDate = ref.read(notificationHistoryProvider.notifier).notificationsByDate;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Notifications",
        showBackButton: true,
        actions: [
          if (notificationState.notifications.isNotEmpty)
            IconButton(
              onPressed: () => _showClearAllDialog(),
              icon: Icon(
                Icons.clear_all,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(notificationHistoryProvider.notifier).refresh(),
        child: notificationState.isLoading
            ? const Center(child: CircularProgressIndicator())
            : notificationState.error != null
                ? _buildErrorWidget(notificationState.error!)
                : notificationState.notifications.isEmpty
                    ? _buildEmptyState()
                    : _buildNotificationsList(notificationsByDate),
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: MySize.size64,
            color: Colors.red,
          ),
          Space.height(16),
          Text(
            'Error loading notifications',
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          Space.height(8),
          Text(
            error,
            style: TextStyle(
              fontSize: MySize.size14,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          Space.height(16),
          ElevatedButton(
            onPressed: () => ref.read(notificationHistoryProvider.notifier).refresh(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            "assets/svg/new_icons/notification_icon.svg",
            height: MySize.size64,
            width: MySize.size64,
            colorFilter: ColorFilter.mode(
              Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.5) ?? Colors.grey,
              BlendMode.srcIn,
            ),
          ),
          Space.height(16),
          Text(
            'No notifications yet',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          Space.height(8),
          Text(
            'Your medication reminders and other notifications will appear here',
            style: TextStyle(
              fontSize: MySize.size14,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(Map<String, List<NotificationHistory>> notificationsByDate) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(MySize.size16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Pending notifications section
          _buildPendingNotificationsSection(),

          // All notifications grouped by date
          ...notificationsByDate.entries.map((entry) {
            return _buildDateSection(entry.key, entry.value);
          }),
        ],
      ),
    );
  }

  Widget _buildPendingNotificationsSection() {
    final pendingNotifications = ref.read(notificationHistoryProvider.notifier).pendingNotifications;

    if (pendingNotifications.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: MySize.size12,
            vertical: MySize.size8,
          ),
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(MySize.size8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.pending_actions,
                size: MySize.size16,
                color: AppColors.primaryColor,
              ),
              Space.width(8),
              Text(
                'Pending Actions (${pendingNotifications.length})',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ),
        Space.height(12),
        ...pendingNotifications.map((notification) =>
          _buildNotificationCard(notification, isPending: true)
        ),
        Space.height(24),
        Divider(color: Theme.of(context).dividerColor),
        Space.height(16),
      ],
    );
  }

  Widget _buildDateSection(String date, List<NotificationHistory> notifications) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: MySize.size8),
          child: Text(
            date,
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ),
        ...notifications.map((notification) =>
          _buildNotificationCard(notification)
        ),
        Space.height(16),
      ],
    );
  }

  Widget _buildNotificationCard(NotificationHistory notification, {bool isPending = false}) {
    // Determine background color based on read status
    Color backgroundColor;
    if (!notification.isRead) {
      // Unread notifications have darker/more prominent background
      backgroundColor = Theme.of(context).brightness == Brightness.dark
          ? AppColors.primaryColor.withValues(alpha: 0.1)
          : AppColors.primaryColor.withValues(alpha: 0.05);
    } else {
      // Read notifications have normal background
      backgroundColor = Theme.of(context).scaffoldBackgroundColor;
    }

    return Container(
      margin: EdgeInsets.only(bottom: MySize.size12),
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(MySize.size12),
        border: isPending
            ? Border.all(color: AppColors.primaryColor.withValues(alpha: 0.3), width: 1)
            : !notification.isRead
                ? Border.all(color: AppColors.primaryColor.withValues(alpha: 0.2), width: 1)
                : null,
        boxShadow: [
          !notification.isRead ?
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.15),
            blurRadius: 10,
            offset: const Offset(0, 2),
          )
          : BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Time positioned in top right corner
          Positioned(
            top: 0,
            right: 0,
            child: Text(
              notification.formattedTime,
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              ),
            ),
          ),

          // Status chip positioned in very right side
          Positioned(
            top: MySize.size24,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size8,
                vertical: MySize.size4,
              ),
              decoration: BoxDecoration(
                color: _getStatusColor(notification.status).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(MySize.size12),
              ),
              child: Text(
                _getStatusText(notification.status),
                style: TextStyle(
                  fontSize: MySize.size12,
                  fontWeight: FontWeight.w500,
                  color: _getStatusColor(notification.status),
                ),
              ),
            ),
          ),

          // Main content
          Padding(
            padding: EdgeInsets.only(right: MySize.size80),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Icon and medication name row
                Row(
                  children: [
                    // Status icon
                    Container(
                      padding: EdgeInsets.all(MySize.size8),
                      decoration: BoxDecoration(
                        color: _getStatusColor(notification.status).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(MySize.size8),
                      ),
                      child: Icon(
                        _getStatusIcon(notification.status),
                        size: MySize.size18,
                        color: _getStatusColor(notification.status),
                      ),
                    ),

                    Space.width(12),

                    // Medication name and description
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            notification.medicationName,
                            style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                          Space.height(4),
                          Text(
                            'Your medication was scheduled for ${notification.formattedTime}',
                            style: TextStyle(
                              fontSize: MySize.size12,
                              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),



              // Action buttons only for pending notifications and only in pending section
              if (isPending && notification.isActionable) ...[
                Space.height(12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      'Skip',
                      Colors.orange.shade400,
                      () => _handleNotificationAction(notification.id, 'skipped'),
                    ),
                    Space.width(8),
                    _buildActionButton(
                      'Taken',
                      AppColors.primary,
                      () => _handleNotificationAction(notification.id, 'taken'),
                    ),
                  ],
                ),
              ],
            ],
          ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String text, Color color, VoidCallback onPressed) {
    return SizedBox(
      height: MySize.size32,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: MySize.size12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.size6),
          ),
          elevation: 0,
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: MySize.size12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'taken':
        return Colors.green;
      case 'skipped':
        return Colors.orange;
      case 'missed':
        return Colors.red;
      default:
        return AppColors.primaryColor;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'taken':
        return Icons.check_circle;
      case 'skipped':
        return Icons.schedule;
      case 'missed':
        return Icons.cancel;
      default:
        return Icons.notifications;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'taken':
        return 'Taken';
      case 'skipped':
        return 'Skipped';
      case 'missed':
        return 'Missed';
      default:
        return 'Pending';
    }
  }

  Future<void> _handleNotificationAction(String notificationId, String status) async {
    try {
      await ref.read(notificationHistoryProvider.notifier)
          .updateNotificationStatus(notificationId, status);

      if (mounted) {
        customSnackBar(
          context,
          'Medication marked as $status',
          color: status == 'taken' ? Colors.green : Colors.orange,
        );
      }
    } catch (e) {
      if (mounted) {
        customSnackBar(
          context,
          'Error updating medication status',
          color: Colors.red,
        );
      }
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notification history? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(notificationHistoryProvider.notifier).clearNotifications();
              customSnackBar(
                context,
                'All notifications cleared',
                color: Colors.green,
              );
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}