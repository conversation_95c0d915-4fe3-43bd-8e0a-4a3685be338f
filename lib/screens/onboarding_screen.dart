import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/utils/snackbar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/period_provider.dart';
import 'package:healo/route/route_constants.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final PageController _pageController = PageController();
  final TextEditingController _dateController = TextEditingController();
  int _currentPage = 0;
  double _cycleLength = 28;
  double _periodLength = 5;

  void _nextPage() {
    if (_pageController.page?.round() == 0 && _dateController.text.isEmpty) {
      customSnackBar(
        context,
        "Please select a date before continuing.",
        color: AppColors.red,
      );
      return;
    }
    if (_currentPage < 2) {
      _pageController.nextPage(
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
  }

  void _prevPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
  }

  Widget _buildStepIndicator(int step) {
    bool isCompleted = step < _currentPage;
    bool isCurrent = step == _currentPage;

    return Container(
      width: MySize.size40,
      height: MySize.size40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isCompleted || isCurrent
            ? AppColors.primaryColor
            : Colors.grey.shade300,
      ),
      child: Center(
        child: Text(
          '${step + 1}',
          style: TextStyle(
            color: isCompleted || isCurrent ? Colors.white : Colors.grey,
            fontWeight: FontWeight.bold,
            fontSize: MySize.size16,
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildStepIndicator(0),
        _buildConnectingLine(_currentPage > 0),
        _buildStepIndicator(1),
        _buildConnectingLine(_currentPage > 1),
        _buildStepIndicator(2),
      ],
    );
  }

  Widget _buildConnectingLine(bool isActive) {
    return Row(
      children: [
        Container(
          width: MySize.size100,
          height: 2,
          color: isActive ? AppColors.primaryColor : Colors.grey.shade300,
        ),
        Icon(
          Icons.arrow_forward_ios,
          color: isActive ? AppColors.primaryColor : Colors.grey.shade300,
          size: MySize.size12,
        ),
      ],
    );
  }

  Future<void> _pickDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 0)),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primaryColor,
              // Header and selected day color
              onPrimary: Colors.white,
              // Header text and selected day text color
              surface: AppColors.white,
              // Calendar background color
              onSurface: AppColors.black, // Calendar text color
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      final formatted = "${picked.day.toString().padLeft(2, '0')}-"
          "${picked.month.toString().padLeft(2, '0')}-"
          "${picked.year}";
      _dateController.text = formatted;
    }
  }

  List<Widget> _buildPages() {
    return [
      Column(
        children: [
          // Illustration at the top
          Expanded(
            flex: 3,
            child: Center(
              child: SizedBox(
                height: MySize.size300,
                width: double.infinity,
                child: Image.asset(
                  'assets/png/onboarding_1.png',
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
          // Content Card at the bottom
          Container(
            width: double.infinity,
            margin: EdgeInsets.all(MySize.size24),
            padding: EdgeInsets.all(MySize.size24),
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(MySize.size20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryColor.withAlpha(50),
                  blurRadius: 10,
                  offset: const Offset(2, 6),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "When Did Your Last Period Start?",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
                Space.height(10),
                Text(
                  "This Help us Calculate Your Cycle and make Accurate Predictions",
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.white.withValues(alpha: 0.9),
                  ),
                ),
                Space.height(30),
                TextField(
                  controller: _dateController,
                  readOnly: true,
                  style: TextStyle(color: AppColors.white),
                  decoration: InputDecoration(
                    hintText: "dd-mm-yyyy",
                    hintStyle: TextStyle(color: AppColors.white),
                    suffixIcon: IconButton(
                      icon: Icon(Icons.calendar_today, color: AppColors.white),
                      onPressed: _pickDate,
                    ),
                    filled: true,
                    fillColor: AppColors.primary,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.white, width: 1),
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.white, width: 1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.white, width: 1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.white, width: 1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                Space.height(30),
                SizedBox(
                  width: double.infinity,
                  height: MySize.size50,
                  child: ElevatedButton(
                    onPressed: _nextPage,
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size10),
                      ),
                      backgroundColor: AppColors.white,
                      foregroundColor: AppColors.primaryColor,
                    ),
                    child: const Text(
                      "Continue",
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      Column(
        children: [
          // Illustration at the top
          Expanded(
            flex: 3,
            child: Center(
              child: SizedBox(
                height: MySize.size300,
                width: double.infinity,
                child: Image.asset(
                  'assets/png/onboarding_2.png',
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
          // Content Card at the bottom
          Container(
            width: double.infinity,
            margin: EdgeInsets.all(MySize.size24),
            padding: EdgeInsets.all(MySize.size24),
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(MySize.size20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryColor.withAlpha(50),
                  blurRadius: 10,
                  offset: const Offset(2, 6),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "How Long is Your Cycle Usually?",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
                Space.height(10),
                Text(
                  "A Cycle Starts on day 1 of your Period and ends the day before your next Period",
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.white.withValues(alpha: 0.9),
                  ),
                ),
                Space.height(30),
                Center(
                  child: Text(
                    "${_cycleLength.toInt()} Days",
                    style: TextStyle(
                      fontSize: MySize.size20,
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Space.height(20),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 16,
                    inactiveTrackColor: Colors.white,
                    activeTrackColor: Colors.white,
                    thumbColor: Color(0xFF009EB3),
                    overlayColor: AppColors.white100,
                    thumbShape: RoundSliderThumbShape(enabledThumbRadius: 12),
                    trackShape: RoundedRectSliderTrackShape(),
                  ),
                  child: Slider(
                    value: _cycleLength,
                    min: 21,
                    max: 45,
                    onChanged: (value) {
                      setState(() {
                        _cycleLength = value;
                      });
                    },
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size32),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "21 Days",
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                      ),
                      Text(
                        "33 Days",
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                      ),
                      Text(
                        "45 Days",
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                Space.height(30),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: MySize.size50,
                        child: OutlinedButton(
                          onPressed: _prevPage,
                          style: OutlinedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(MySize.size10),
                            ),
                            side: BorderSide(color: AppColors.white),
                            foregroundColor: AppColors.white,
                          ),
                          child: const Text(
                            "Back",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                    ),
                    Space.width(16),
                    Expanded(
                      child: SizedBox(
                        height: MySize.size50,
                        child: ElevatedButton(
                          onPressed: _nextPage,
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(MySize.size10),
                            ),
                            backgroundColor: AppColors.white,
                            foregroundColor: AppColors.primaryColor,
                          ),
                          child: const Text(
                            "Continue",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      Column(
        children: [
          // Illustration at the top
          Expanded(
            flex: 3,
            child: Center(
              child: SizedBox(
                height: MySize.size300,
                width: double.infinity,
                child: Image.asset(
                  'assets/png/onboarding_3.png',
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
          // Content Card at the bottom
          Container(
            width: double.infinity,
            margin: EdgeInsets.all(MySize.size24),
            padding: EdgeInsets.all(MySize.size24),
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(MySize.size20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryColor.withAlpha(50),
                  blurRadius: 10,
                  offset: const Offset(2, 6),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "How many days does your period usually Last?",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
                Space.height(10),
                Text(
                  "This is the Number of days you Typically bleed During Your Period",
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.white.withValues(alpha: 0.9),
                  ),
                ),
                Space.height(30),
                Center(
                  child: Text(
                    "${_periodLength.toInt()} Days",
                    style: TextStyle(
                      fontSize: MySize.size20,
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Space.height(20),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 16,
                    inactiveTrackColor: Colors.white,
                    activeTrackColor: Colors.white,
                    thumbColor: Color(0xFF009EB3),
                    overlayColor: AppColors.white100,
                    thumbShape: RoundSliderThumbShape(enabledThumbRadius: 12),
                    trackShape: RoundedRectSliderTrackShape(),
                  ),
                  child: Slider(
                    value: _periodLength,
                    min: 1,
                    max: 10,
                    onChanged: (value) {
                      setState(() {
                        _periodLength = value;
                      });
                    },
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size32),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "1",
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                      ),
                      Text(
                        "5",
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                      ),
                      Text(
                        "10",
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                Space.height(30),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: MySize.size50,
                        child: OutlinedButton(
                          onPressed: _prevPage,
                          style: OutlinedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(MySize.size10),
                            ),
                            side: BorderSide(color: AppColors.white),
                            foregroundColor: AppColors.white,
                          ),
                          child: const Text(
                            "Back",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                    ),
                    Space.width(16),
                    Expanded(
                      child: SizedBox(
                        height: MySize.size50,
                        child: ElevatedButton(
                          onPressed: () async {
                            log(_periodLength.toString());
                            log(_cycleLength.toString());
                            log(_dateController.text);
                            await ref
                                .read(periodProvider.notifier)
                                .addPeriodData(
                                  lastPeriodDateStr: _dateController.text,
                                  cycleLength: _cycleLength.toInt(),
                                  periodDuration: _periodLength.toInt(),
                                );
                            ref
                                .read(periodProvider.notifier)
                                .fetchPeriodHistory();
                            if (mounted) {
                              Navigator.popAndPushNamed(
                                  context, periodTrackerScreen);
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(MySize.size10),
                            ),
                            backgroundColor: AppColors.white,
                            foregroundColor: AppColors.primaryColor,
                          ),
                          child: const Text(
                            "Continue",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            const CustomAppBar(title: "Tell Us About Your Cycle"),
            Space.height(20),
            _buildStepIndicators(),
            Space.height(30),
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() => _currentPage = index);
                },
                children: _buildPages(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
