import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:healo/models/daily_medication_model.dart';
import 'package:healo/models/monthly_medication_model.dart';
import 'package:healo/models/weekly_medication_model.dart';
import 'package:healo/models/medication_intake_model.dart';
import 'package:healo/route/route_constants.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/providers/medication_intake_provider.dart';
import 'package:healo/providers/notification_history_provider.dart';
import 'package:intl/intl.dart';
import 'dart:developer';

class MedicationListScreen extends ConsumerStatefulWidget {
  const MedicationListScreen({super.key});

  @override
  ConsumerState<MedicationListScreen> createState() =>
      _MedicationListScreenState();
}

class _MedicationListScreenState extends ConsumerState<MedicationListScreen> {
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(medicationProvider.notifier).fetchDailyMedicationHistory();
      ref
          .read(weeklyMedicationProvider.notifier)
          .fetchWeeklyMedicationHistory();
      ref
          .read(monthlyMedicationProvider.notifier)
          .fetchMonthlyMedicationHistory();

      // Fetch medication intake data
      ref.read(medicationIntakeProvider.notifier).fetchAllIntakes();

      // Check if coming from notification
      _checkForNotificationData();
    });
  }

  /// Check if screen was opened from notification and show dialog
  void _checkForNotificationData() {
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && args is Map<String, dynamic>) {
      final fromNotification = args['from_notification'] as bool? ?? false;
      if (fromNotification) {
        final medicationName = args['medication_name'] as String? ?? '';
        final time = args['time'] as String? ?? '';
        final dosage = args['dosage'] as String? ?? '';
        final notificationId = args['notification_id'] as String? ?? '';
        final appWasAlreadyOpen = args['app_was_already_open'] as bool? ?? false;

        if (medicationName.isNotEmpty) {
          // Show dialog after a short delay to ensure screen is built
          Future.delayed(const Duration(milliseconds: 300), () {
            _showMedicationDialog(medicationName, time, dosage, notificationId: notificationId, appWasAlreadyOpen: appWasAlreadyOpen);
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final weeklyMedications = ref.watch(weeklyMedicationProvider);
    final dailyMedications = ref.watch(medicationProvider);
    final monthlyMedications = ref.watch(monthlyMedicationProvider);

    final selected = _selectedDate ?? DateTime.now();
    final dateRange = List.generate(5, (i) {
      return selected.subtract(Duration(days: 2 - i));
    });

    List<DailyMedication> filteredDaily = dailyMedications.where((med) {
      try {
        // Check if medication was created on or before the selected date
        final createdParts = med.createdDate.split('/');
        final createdDate = DateTime(
          int.parse(createdParts[2]),
          int.parse(createdParts[1]),
          int.parse(createdParts[0]),
        );

        // Check if medication hasn't expired
        final expiryParts = med.expiryDate.split('/');
        final expiryDate = DateTime(
          int.parse(expiryParts[2]),
          int.parse(expiryParts[1]),
          int.parse(expiryParts[0]),
        );

        // Show medication only if:
        // 1. It was created on or before the selected date
        // 2. It hasn't expired yet (expiry date is after selected date)
        return (createdDate.isBefore(selected) || createdDate.isAtSameMomentAs(selected)) &&
               expiryDate.isAfter(selected);
      } catch (_) {
        // If date parsing fails, show the medication (fallback for old data without createdDate)
        try {
          final parts = med.expiryDate.split('/');
          final expiry = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
          return expiry.isAfter(selected);
        } catch (_) {
          return true;
        }
      }
    }).toList();

    List<WeeklyMedication> filteredWeekly = weeklyMedications.where((med) {
      try {
        // Check if medication was created on or before the selected date
        final createdParts = med.createdDate.split('/');
        final createdDate = DateTime(
          int.parse(createdParts[2]),
          int.parse(createdParts[1]),
          int.parse(createdParts[0]),
        );

        // Check if medication hasn't expired
        final expiryParts = med.expiryDate.split('/');
        final expiryDate = DateTime(
          int.parse(expiryParts[2]),
          int.parse(expiryParts[1]),
          int.parse(expiryParts[0]),
        );

        final isNotExpired = expiryDate.isAfter(selected);
        final weekday = DateFormat('EEEE').format(selected);
        final isScheduledToday = med.days.contains(weekday);
        final wasCreatedBeforeOrOnSelectedDate = createdDate.isBefore(selected) || createdDate.isAtSameMomentAs(selected);

        return wasCreatedBeforeOrOnSelectedDate && isNotExpired && isScheduledToday;
      } catch (_) {
        // If date parsing fails, use fallback logic for old data without createdDate
        try {
          final parts = med.expiryDate.split('/');
          final expiry = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
          final isNotExpired = expiry.isAfter(selected);
          final weekday = DateFormat('EEEE').format(selected);
          final isScheduledToday = med.days.contains(weekday);

          return isNotExpired && isScheduledToday;
        } catch (_) {
          return false;
        }
      }
    }).toList();

    List<MonthlyMedication> filteredMonthly = monthlyMedications.where((med) {
      try {
        // Check if medication was created on or before the selected date
        final createdParts = med.createdDate.split('/');
        final createdDate = DateTime(
          int.parse(createdParts[2]),
          int.parse(createdParts[1]),
          int.parse(createdParts[0]),
        );

        // Check if medication hasn't expired
        final expiryParts = med.expiryDate.split('/');
        final expiryDate = DateTime(
          int.parse(expiryParts[2]),
          int.parse(expiryParts[1]),
          int.parse(expiryParts[0]),
        );

        if (!expiryDate.isAfter(selected)) return false;

        // Check if medication was created before or on the selected date
        if (!(createdDate.isBefore(selected) || createdDate.isAtSameMomentAs(selected))) return false;

        final selectedDay = selected.day;

        final isScheduledToday = med.dates.any((dateStr) {
          final parts = dateStr?.split('/');
          final day = int.tryParse(parts![0]);
          return day == selectedDay;
        });

        return isScheduledToday;
      } catch (_) {
        // If date parsing fails, use fallback logic for old data without createdDate
        try {
          final parts = med.expiryDate.split('/');
          final expiry = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
          if (!expiry.isAfter(selected)) return false;

          final selectedDay = selected.day;

          final isScheduledToday = med.dates.any((dateStr) {
            final parts = dateStr?.split('/');
            final day = int.tryParse(parts![0]);
            return day == selectedDay;
          });

          return isScheduledToday;
        } catch (_) {
          return false;
        }
      }
    }).toList();

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "My Medications",
        // actions: [
        //   IconButton(
        //     icon: const Icon(Icons.history),
        //     onPressed: () {
        //       _showIntakeHistory();
        //     },
        //   ),
        // ],
      ),
      body: dailyMedications.isEmpty &&
              weeklyMedications.isEmpty &&
              monthlyMedications.isEmpty
          ? const Center(child: Text("No medications found"))
          : Padding(
              padding: EdgeInsets.only(
                  left: MySize.size15,
                  right: MySize.size15,
                  bottom: MySize.size85),
              child: ListView(
                scrollDirection: Axis.vertical,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _monthName(_selectedDate!.month),
                        style: TextStyle(
                          fontSize: MySize.size22,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.calendar_today,
                            color: AppColors.primaryColor),
                        onPressed: () async {
                          final pickedDate = await showDatePicker(
                            context: context,
                            initialDate: _selectedDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2100),
                          );

                          if (pickedDate != null) {
                            setState(() {
                              _selectedDate = pickedDate;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  SizedBox(
                    height: MySize.size74,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: dateRange.map((date) {
                        return Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedDate = date;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: MySize.size5),
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: AppColors.primaryColor),
                                color: _isSameDate(date, _selectedDate!)
                                    ? AppColors.primaryColor
                                    : Theme.of(context).scaffoldBackgroundColor,
                                borderRadius:
                                    BorderRadius.circular(MySize.size20),
                              ),
                              padding:
                                  EdgeInsets.symmetric(vertical: MySize.size12),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    _weekdayShort(date),
                                    style: TextStyle(
                                      color: _isSameDate(date, _selectedDate!)
                                          ? AppColors.white
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                      fontSize: MySize.size12,
                                    ),
                                  ),
                                  Text(
                                    "${date.day}",
                                    style: TextStyle(
                                      color: _isSameDate(date, _selectedDate!)
                                          ? AppColors.white
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  Space.height(20),
                  // Today's Medications Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Today's Medications",
                        style: TextStyle(
                          fontSize: MySize.size20,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                      Text(
                        DateFormat('EEE, MMM d').format(_selectedDate!),
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Space.height(16),

                  // Today's Medications List
                  ...(_buildTodaysMedications(filteredDaily, filteredWeekly, filteredMonthly)),

                  Space.height(24),

                  // Upcoming Medications Section
                  Text(
                    "Upcoming Medications",
                    style: TextStyle(
                      fontSize: MySize.size20,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                  Space.height(16),

                  // Tomorrow's medications
                  _buildUpcomingMedications()
                ],
              ),
            ),
      floatingActionButton: SizedBox(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MySize.size50,
        child: FloatingActionButton.extended(
          onPressed: () {
            Navigator.pushNamed(context, addmedicationScreen);
          },
          backgroundColor: AppColors.primaryColor,
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.size25),
          ),
          label: Text(
            "Add Medication",
            style: TextStyle(
              color: AppColors.white,
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  /// Build today's medications with new design
  List<Widget> _buildTodaysMedications(List<DailyMedication> daily, List<WeeklyMedication> weekly, List<MonthlyMedication> monthly) {
    List<Widget> medications = [];

    // Add daily medications
    for (var med in daily) {
      medications.add(_buildMedicationCard(
        medicationName: med.medicineName,
        displayName: "${med.medicineName} ${med.quantity}${med.unit}",
        frequency: med.frequency,
        dosage: med.dosage,
        timing: med.timing,
        onEdit: () => Navigator.pushNamed(context, addmedicationScreen, arguments: med),
        onDelete: () => _deleteDailyMedication(med),
      ));
    }

    // Add weekly medications
    for (var med in weekly) {
      medications.add(_buildMedicationCard(
        medicationName: med.medicineName,
        displayName: "${med.medicineName} ${med.quantity}${med.unit}",
        frequency: med.frequency,
        dosage: med.dosage,
        timing: med.timing,
        onEdit: () => Navigator.pushNamed(context, addmedicationScreen, arguments: med),
        onDelete: () => _deleteWeeklyMedication(med),
      ));
    }

    // Add monthly medications
    for (var med in monthly) {
      medications.add(_buildMedicationCard(
        medicationName: med.medicineName,
        displayName: "${med.medicineName} ${med.quantity}${med.unit}",
        frequency: med.frequency,
        dosage: med.dosage,
        timing: med.timing,
        onEdit: () => Navigator.pushNamed(context, addmedicationScreen, arguments: med),
        onDelete: () => _deleteMonthlyMedication(med),
      ));
    }

    if (medications.isEmpty) {
      medications.add(
        Container(
          padding: EdgeInsets.all(MySize.size20),
          child: Center(
            child: Text(
              "No medications scheduled for today",
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: MySize.size14,
              ),
            ),
          ),
        ),
      );
    }

    return medications;
  }

  /// Build individual medication card with new design
  Widget _buildMedicationCard({
    required String medicationName, // The actual medication name for Firebase lookup
    required String displayName, // The display name with quantity and unit
    required String frequency,
    required String dosage,
    required List<String> timing,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    // Get medication intake data from provider
    final medicationIntakes = ref.watch(medicationIntakeProvider);
    final today = DateFormat('dd-MM-yyyy').format(_selectedDate ?? DateTime.now());

    // Calculate progress based on real intake data
    int totalDoses = timing.length;
    int takenDoses = 0;
    List<String> takenTimes = [];
    List<String> skippedTimes = [];
    List<String> dueTimes = [];
    String? nextDoseTime;

    // Check each timing for intake status
    for (String time in timing) {
      final intake = medicationIntakes.firstWhere(
        (intake) =>
          intake.medicationName == medicationName &&
          intake.date == today &&
          intake.time == time,
        orElse: () => MedicationIntake(
          medicationName: '',
          date: '',
          time: '',
          status: '',
          timestamp: Timestamp.now(),
        ),
      );

      if (intake.medicationName.isNotEmpty) {
        if (intake.status == 'taken') {
          takenDoses++;
          takenTimes.add(time);
        } else if (intake.status == 'skipped') {
          skippedTimes.add(time);
        }
      } else {
        dueTimes.add(time);
        // Set next dose time to the first due time
        if (nextDoseTime == null) {
          final now = DateTime.now();
          final timeFormat = DateFormat('h:mm a');
          try {
            final doseTime = timeFormat.parse(time);
            final todayDoseTime = DateTime(now.year, now.month, now.day, doseTime.hour, doseTime.minute);
            if (todayDoseTime.isAfter(now)) {
              nextDoseTime = time;
            }
          } catch (e) {
            // If parsing fails, just use the time as is
            nextDoseTime = time;
          }
        }
      }
    }

    // If no future doses today, show the first due time
    if (nextDoseTime == null && dueTimes.isNotEmpty) {
      nextDoseTime = dueTimes.first;
    }

    return Dismissible(
      key: ValueKey(medicationName),
      background: Container(
        alignment: Alignment.centerRight,
        decoration: BoxDecoration(
          color: AppColors.red,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        margin: EdgeInsets.only(bottom: MySize.size12),
        padding: EdgeInsets.symmetric(horizontal: MySize.size20),
        child: const Icon(Icons.delete, color: AppColors.white),
      ),
      direction: DismissDirection.endToStart,
      confirmDismiss: (direction) async {
        return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Theme.of(context).cardColor,
            title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
            content: const Text("Are you sure you want to delete this medication?"),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text("Delete", style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        );
      },
      onDismissed: (direction) => onDelete(),
      child: GestureDetector(
        onTap: onEdit, // Make the entire card clickable to edit
        child: Container(
          margin: EdgeInsets.only(bottom: MySize.size12),
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(MySize.size16),
            border: Border.all(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryColor.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(2, 6),
              ),
            ],
          ),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with medicine icon and next dose time
          Row(
            children: [
              // Medicine icon
              Container(
                width: MySize.size48,
                height: MySize.size48,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(MySize.size12),
                  child: SvgPicture.asset('assets/svg/medication_icon.svg',
                    colorFilter: const ColorFilter.mode(
                      Colors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              Space.width(12),
              // Medicine details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayName,
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                    Space.height(2),
                    Text(
                      "$totalDoses Times Daily",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
              ),
              // Next dose time and edit indicator
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (nextDoseTime != null && nextDoseTime.isNotEmpty) ...[
                    Space.height(4),
                    Text(
                      "Next: $nextDoseTime",
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),

          Space.height(16),

          // Progress bar
          Row(
            children: [
              Expanded(
                child: Container(
                  height: MySize.size6,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(MySize.size3),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: takenDoses / totalDoses,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        borderRadius: BorderRadius.circular(MySize.size3),
                      ),
                    ),
                  ),
                ),
              ),
              Space.width(8),
              Text(
                "$takenDoses/$totalDoses Taken",
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ),

          Space.height(12),

          // Timing details - show real taken, skipped, and due times
          if (takenTimes.isNotEmpty || skippedTimes.isNotEmpty || dueTimes.isNotEmpty)
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                // Show taken medications
                ...takenTimes.map((time) => Container(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(MySize.size12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.check, color: Colors.white, size: MySize.size12),
                      Space.width(4),
                      Text(
                        "$time (Taken)",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MySize.size10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )),

                // Show skipped medications (clickable to mark as taken)
                ...skippedTimes.map((time) => GestureDetector(
                  onTap: () => _showUpdateSkippedDialog(medicationName, time),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(MySize.size12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.close, color: Colors.white, size: MySize.size12),
                        Space.width(4),
                        Text(
                          "$time (Skipped)",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: MySize.size10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Space.width(4),
                        Icon(Icons.edit, color: Colors.white, size: MySize.size10),
                      ],
                    ),
                  ),
                )),

                // Show due medications
                ...dueTimes.map((time) => Container(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(MySize.size12),
                    border: Border.all(color: AppColors.primaryColor.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.schedule, color: AppColors.primaryColor, size: MySize.size12),
                      Space.width(4),
                      Text(
                        "$time (Due)",
                        style: TextStyle(
                          color: AppColors.primaryColor,
                          fontSize: MySize.size10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ),
        ],
      ),
        ),
      ),
    );
  }

  /// Build upcoming medications section
  Widget _buildUpcomingMedications() {
    final tomorrow = _selectedDate!.add(const Duration(days: 1));
    final dailyMedications = ref.watch(medicationProvider);
    final weeklyMedications = ref.watch(weeklyMedicationProvider);
    final monthlyMedications = ref.watch(monthlyMedicationProvider);

    List<Widget> upcomingMeds = [];

    // Get tomorrow's daily medications
    List<DailyMedication> tomorrowDaily = dailyMedications.where((med) {
      try {
        // Check if medication was created on or before tomorrow
        final createdParts = med.createdDate.split('/');
        final createdDate = DateTime(
          int.parse(createdParts[2]),
          int.parse(createdParts[1]),
          int.parse(createdParts[0]),
        );

        // Check if medication hasn't expired
        final expiryParts = med.expiryDate.split('/');
        final expiryDate = DateTime(
          int.parse(expiryParts[2]),
          int.parse(expiryParts[1]),
          int.parse(expiryParts[0]),
        );

        // Show medication only if:
        // 1. It was created on or before tomorrow
        // 2. It hasn't expired yet (expiry date is after tomorrow)
        return (createdDate.isBefore(tomorrow) || createdDate.isAtSameMomentAs(tomorrow)) &&
               expiryDate.isAfter(tomorrow);
      } catch (_) {
        // If date parsing fails, use fallback logic for old data without createdDate
        try {
          final parts = med.expiryDate.split('/');
          final expiry = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
          return expiry.isAfter(tomorrow);
        } catch (_) {
          return true;
        }
      }
    }).toList();

    // Get tomorrow's weekly medications
    List<WeeklyMedication> tomorrowWeekly = weeklyMedications.where((med) {
      try {
        // Check if medication was created on or before tomorrow
        final createdParts = med.createdDate.split('/');
        final createdDate = DateTime(
          int.parse(createdParts[2]),
          int.parse(createdParts[1]),
          int.parse(createdParts[0]),
        );

        // Check if medication hasn't expired
        final expiryParts = med.expiryDate.split('/');
        final expiryDate = DateTime(
          int.parse(expiryParts[2]),
          int.parse(expiryParts[1]),
          int.parse(expiryParts[0]),
        );

        final isNotExpired = expiryDate.isAfter(tomorrow);
        final weekday = DateFormat('EEEE').format(tomorrow);
        final isScheduledTomorrow = med.days.contains(weekday);
        final wasCreatedBeforeOrOnTomorrow = createdDate.isBefore(tomorrow) || createdDate.isAtSameMomentAs(tomorrow);

        return wasCreatedBeforeOrOnTomorrow && isNotExpired && isScheduledTomorrow;
      } catch (_) {
        // If date parsing fails, use fallback logic for old data without createdDate
        try {
          final parts = med.expiryDate.split('/');
          final expiry = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
          final isNotExpired = expiry.isAfter(tomorrow);
          final weekday = DateFormat('EEEE').format(tomorrow);
          final isScheduledTomorrow = med.days.contains(weekday);

          return isNotExpired && isScheduledTomorrow;
        } catch (_) {
          return false;
        }
      }
    }).toList();

    // Get tomorrow's monthly medications
    List<MonthlyMedication> tomorrowMonthly = monthlyMedications.where((med) {
      try {
        // Check if medication was created on or before tomorrow
        final createdParts = med.createdDate.split('/');
        final createdDate = DateTime(
          int.parse(createdParts[2]),
          int.parse(createdParts[1]),
          int.parse(createdParts[0]),
        );

        // Check if medication hasn't expired
        final expiryParts = med.expiryDate.split('/');
        final expiryDate = DateTime(
          int.parse(expiryParts[2]),
          int.parse(expiryParts[1]),
          int.parse(expiryParts[0]),
        );

        if (!expiryDate.isAfter(tomorrow)) return false;

        // Check if medication was created before or on tomorrow
        if (!(createdDate.isBefore(tomorrow) || createdDate.isAtSameMomentAs(tomorrow))) return false;

        final tomorrowDay = tomorrow.day;
        final isScheduledTomorrow = med.dates.any((dateStr) {
          final parts = dateStr?.split('/');
          final day = int.tryParse(parts![0]);
          return day == tomorrowDay;
        });

        return isScheduledTomorrow;
      } catch (_) {
        // If date parsing fails, use fallback logic for old data without createdDate
        try {
          final parts = med.expiryDate.split('/');
          final expiry = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
          if (!expiry.isAfter(tomorrow)) return false;

          final tomorrowDay = tomorrow.day;
          final isScheduledTomorrow = med.dates.any((dateStr) {
            final parts = dateStr?.split('/');
            final day = int.tryParse(parts![0]);
            return day == tomorrowDay;
          });

          return isScheduledTomorrow;
        } catch (_) {
          return false;
        }
      }
    }).toList();

    // Add daily medications
    for (var med in tomorrowDaily) {
      upcomingMeds.add(_buildUpcomingMedicationItem(
        "${med.medicineName} ${med.quantity}${med.unit}",
        med.timing.join(', '),
      ));
      upcomingMeds.add(Space.height(8));
    }

    // Add weekly medications
    for (var med in tomorrowWeekly) {
      upcomingMeds.add(_buildUpcomingMedicationItem(
        "${med.medicineName} ${med.quantity}${med.unit}",
        med.timing.join(', '),
      ));
      upcomingMeds.add(Space.height(8));
    }

    // Add monthly medications
    for (var med in tomorrowMonthly) {
      upcomingMeds.add(_buildUpcomingMedicationItem(
        "${med.medicineName} ${med.quantity}${med.unit}",
        med.timing.join(', '),
      ));
      upcomingMeds.add(Space.height(8));
    }

    // Remove last spacing
    if (upcomingMeds.isNotEmpty) {
      upcomingMeds.removeLast();
    }

    return Container(
      margin: EdgeInsets.only(bottom: MySize.size12),
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tomorrow header
          Text(
            "Tomorrow, ${DateFormat('MMMM d').format(tomorrow)}",
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          Space.height(12),

          // Show upcoming medications or no data message
          if (upcomingMeds.isNotEmpty)
            ...upcomingMeds
          else
            Padding(
              padding: EdgeInsets.all(MySize.size10),
              child: Text(
                "No medications scheduled for tomorrow",
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                  fontSize: MySize.size14,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build upcoming medication item
  Widget _buildUpcomingMedicationItem(String medicineName, String timing) {
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.medication_outlined,
              color: AppColors.primaryColor,
              size: MySize.size16,
            ),
            Space.width(8),
            Text(
              "$medicineName - $timing",
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.black100,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        Space.height(8),
      ],
    );
  }

  /// Delete daily medication with confirmation
  Future<void> _deleteDailyMedication(DailyMedication med) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
        content: const Text("Are you sure you want to delete this medication?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text("Delete", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(medicationProvider.notifier).deleteDailyMedication(med.medicineName);
    }
  }

  /// Delete weekly medication with confirmation
  Future<void> _deleteWeeklyMedication(WeeklyMedication med) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
        content: const Text("Are you sure you want to delete this medication?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text("Delete", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(weeklyMedicationProvider.notifier).deleteWeeklyMedication(med.medicineName);
    }
  }

  /// Delete monthly medication with confirmation
  Future<void> _deleteMonthlyMedication(MonthlyMedication med) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
        content: const Text("Are you sure you want to delete this medication?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text("Delete", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(monthlyMedicationProvider.notifier).deleteMonthlyMedication(med.medicineName);
    }
  }

  /// Show dialog to update skipped medication to taken
  Future<void> _showUpdateSkippedDialog(String medicationName, String time) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text(
          "Update Medication Status",
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyLarge?.color,
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Do you want to mark this medication as taken?",
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: MySize.size14,
              ),
            ),
            Space.height(12),
            Container(
              padding: EdgeInsets.all(MySize.size12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(MySize.size8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.medication, color: Colors.orange, size: MySize.size16),
                  Space.width(8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          medicationName,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: MySize.size14,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                        Text(
                          "$time (Currently Skipped)",
                          style: TextStyle(
                            fontSize: MySize.size12,
                            color: AppColors.black100,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              "Cancel",
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: MySize.size14,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: MySize.size16, vertical: MySize.size8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.check, size: MySize.size16),
                Space.width(4),
                Text(
                  "Mark as Taken",
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _updateSkippedToTaken(medicationName, time);
    }
  }

  /// Update skipped medication to taken status
  Future<void> _updateSkippedToTaken(String medicationName, String time) async {
    try {
      final today = DateFormat('dd-MM-yyyy').format(_selectedDate ?? DateTime.now());

      // Update the intake status from skipped to taken
      await ref.read(medicationIntakeProvider.notifier).updateIntakeStatus(
        medicationName,
        today,
        time,
        'taken',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: MySize.size20),
                Space.width(8),
                Expanded(
                  child: Text(
                    '$medicationName at $time marked as taken',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating medication status: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  String _monthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  bool _isSameDate(DateTime a, DateTime b) =>
      a.year == b.year && a.month == b.month && a.day == b.day;

  String _weekdayShort(DateTime date) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.weekday % 7];
  }

  // /// Show medication intake history dialog
  // void _showIntakeHistory() async {
  //   try {
  //     // Fetch all intake records
  //     await ref.read(medicationIntakeProvider.notifier).fetchAllIntakes();
  //     final intakes = ref.read(medicationIntakeProvider);

  //     if (!mounted) return;

  //     showDialog(
  //       context: context,
  //       builder: (context) => AlertDialog(
  //         backgroundColor: Theme.of(context).cardColor,
  //         title: Text(
  //           'Medication History',
  //           style: TextStyle(
  //             color: Theme.of(context).textTheme.bodyLarge?.color,
  //           ),
  //         ),
  //         content: SizedBox(
  //           width: double.maxFinite,
  //           height: 400,
  //           child: intakes.isEmpty
  //               ? const Center(child: Text('No intake records found'))
  //               : ListView.builder(
  //                   itemCount: intakes.length,
  //                   itemBuilder: (context, index) {
  //                     final intake = intakes[index];
  //                     return Card(
  //                       color: Theme.of(context).scaffoldBackgroundColor,
  //                       child: ListTile(
  //                         title: Text(
  //                           intake.medicationName,
  //                           style: TextStyle(
  //                             fontWeight: FontWeight.bold,
  //                             color: Theme.of(context).textTheme.bodyMedium?.color,
  //                           ),
  //                         ),
  //                         subtitle: Text(
  //                           '${intake.date} at ${intake.time}',
  //                           style: TextStyle(
  //                             color: Theme.of(context).textTheme.bodyMedium?.color,
  //                           ),
  //                         ),
  //                         trailing: Container(
  //                           padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
  //                           decoration: BoxDecoration(
  //                             color: intake.status == 'taken'
  //                                 ? Colors.green
  //                                 : intake.status == 'skipped'
  //                                     ? Colors.orange
  //                                     : Colors.red,
  //                             borderRadius: BorderRadius.circular(12),
  //                           ),
  //                           child: Text(
  //                             intake.status.toUpperCase(),
  //                             style: const TextStyle(
  //                               color: Colors.white,
  //                               fontSize: 12,
  //                               fontWeight: FontWeight.bold,
  //                             ),
  //                           ),
  //                         ),
  //                       ),
  //                     );
  //                   },
  //                 ),
  //         ),
  //         actions: [
  //           TextButton(
  //             onPressed: () => Navigator.of(context).pop(),
  //             child: const Text(
  //               'Close',
  //               style: TextStyle(color: AppColors.primaryColor),
  //             ),
  //           ),
  //         ],
  //       ),
  //     );
  //   } catch (e) {
  //     if (mounted) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         SnackBar(content: Text('Error loading intake history: $e')),
  //       );
  //     }
  //   }
  // }

  /// Show medication dialog with Taken/Skip options
  void _showMedicationDialog(String medicationName, String time, String dosage, {String? notificationId, bool appWasAlreadyOpen = false}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.size15),
          ),
          title: Row(
            children: [
              Icon(
                Icons.medication,
                color: AppColors.primaryColor,
                size: MySize.size24,
              ),
              Space.width(10),
              Expanded(
                child: Text(
                  'Medication Reminder',
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Time to take your medication:',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              Space.height(10),
              Container(
                padding: EdgeInsets.all(MySize.size12),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicationName,
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    if (dosage.isNotEmpty) ...[
                      Space.height(4),
                      Text(
                        'Dosage: $dosage',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                    ],
                    if (time.isNotEmpty) ...[
                      Space.height(4),
                      Text(
                        'Time: $time',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Space.height(15),
              Text(
                'Did you take this medication?',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await _recordMedicationAction(medicationName, time, 'skipped', notificationId: notificationId, appWasAlreadyOpen: appWasAlreadyOpen);
                    },
                    icon: const Icon(Icons.close, size: 18),
                    label: const Text('Skip'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black,
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size8),
                      ),
                    ),
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await _recordMedicationAction(medicationName, time, 'taken', notificationId: notificationId, appWasAlreadyOpen: appWasAlreadyOpen);
                    },
                    icon: const Icon(Icons.check, size: 18),
                    label: const Text('Taken'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Record medication action to Firebase
  Future<void> _recordMedicationAction(String medicationName, String time, String status, {String? notificationId, bool appWasAlreadyOpen = false}) async {
    try {
      if (status == 'taken') {
        await ref.read(medicationIntakeProvider.notifier).markMedicationTaken(medicationName, time);
      } else {
        await ref.read(medicationIntakeProvider.notifier).markMedicationSkipped(medicationName, time);
      }

      // Also update notification history if it exists
      await _updateNotificationHistoryForDeviceAction(medicationName, time, status, notificationId: notificationId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$medicationName marked as $status'),
            backgroundColor: status == 'taken' ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );

        // Handle navigation based on app state
        if (appWasAlreadyOpen) {
          // App was already open, close this screen and return to previous screen
          Navigator.of(context).pop();
        } else {
          // App was closed and opened by notification, navigate to home screen
          Navigator.of(context).pushNamedAndRemoveUntil(
            mainScreen,
            (route) => false,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recording medication: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Update notification history when action is taken from device notification
  Future<void> _updateNotificationHistoryForDeviceAction(String medicationName, String time, String status, {String? notificationId}) async {
    try {
      // Get all notifications from the provider
      final notificationState = ref.read(notificationHistoryProvider);

      String targetNotificationId;

      if (notificationId != null && notificationId.isNotEmpty) {
        // Use the specific notification ID from the device notification
        targetNotificationId = notificationId;
        log('Using specific notification ID: $notificationId');
      } else {
        // Fallback: Find the notification that matches this medication and time
        final matchingNotification = notificationState.notifications.firstWhere(
          (notification) =>
            notification.medicationName == medicationName &&
            notification.time == time &&
            notification.status == 'pending', // Only update pending notifications
          orElse: () => throw Exception('No matching notification found'),
        );
        targetNotificationId = matchingNotification.id;
        log('Found matching notification by medication and time: $targetNotificationId');
      }

      // Update the notification status using the provider
      await ref.read(notificationHistoryProvider.notifier)
          .updateNotificationStatus(targetNotificationId, status);

      log('Updated notification history for device action: $medicationName at $time - $status (ID: $targetNotificationId)');
    } catch (e) {
      log('Error updating notification history for device action: $e');
      // Don't show error to user as this is a background operation
      // The main medication logging still succeeded
    }
  }
}
