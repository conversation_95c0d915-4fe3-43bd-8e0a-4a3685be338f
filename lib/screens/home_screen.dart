import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/blood_pressure_provider.dart';
import 'package:healo/providers/hba1c_provider.dart';
import 'package:healo/providers/health_provider.dart';
import 'package:healo/route/route_constants.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/providers/kidney_provider.dart';
import 'package:healo/providers/thyroid_provider.dart';
import 'package:healo/providers/liver_provider.dart';

import 'package:healo/providers/health_data_loader_provider.dart';
import 'package:healo/providers/water_intake_provider.dart';
import 'package:healo/providers/report_provider.dart';
import 'package:healo/providers/bmi_provider.dart';
import 'package:healo/providers/period_provider.dart';
import 'package:healo/providers/diabetes_provider.dart';
import 'package:healo/providers/vitamin_provider.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/providers/notification_history_provider.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:developer';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> with WidgetsBindingObserver {
  Timer? _notificationRefreshTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // Load all health data at once using the health data loader provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      log("HomeScreen: Loading all health data");
      ref.read(healthDataLoaderProvider.notifier).loadAllHealthData();

      // Also force refresh user data to ensure it's up to date
      log("HomeScreen: Invalidating user data providers");
      ref.invalidate(userDataProvider);

      // Initialize notification history provider to enable real-time badge updates
      log("HomeScreen: Initializing notification history provider");
      ref.read(notificationHistoryProvider.notifier).fetchNotificationHistory(showLoading: false);

      // Start periodic refresh for notifications
      _startNotificationRefresh();
    });
  }

  @override
  void dispose() {
    _notificationRefreshTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // Refresh notifications when app comes to foreground
      ref.read(notificationHistoryProvider.notifier).silentRefresh();
      _startNotificationRefresh();
    } else if (state == AppLifecycleState.paused) {
      // Stop refresh when app goes to background
      _notificationRefreshTimer?.cancel();
    }
  }

  /// Start periodic refresh for notification badge
  void _startNotificationRefresh() {
    _notificationRefreshTimer?.cancel();
    _notificationRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        ref.read(notificationHistoryProvider.notifier).silentRefresh();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use stream-based provider for real-time updates
    final userName = ref.watch(userNameStreamProvider);
    final userGender = ref.watch(userGenderProvider);

    return SafeArea(
      child: Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: RefreshIndicator(
            onRefresh: () async {
              await ref
                  .read(healthDataLoaderProvider.notifier)
                  .refreshAllHealthData();
            },
            child: SingleChildScrollView(
              physics: AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.all(MySize.size15),
                child: Column(
                    spacing: MySize.size20,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${_getGreeting()} ${userName != null ? (userName.contains(' ') ? userName.substring(0, userName.indexOf(' ')) : userName) : "User"} 👋",
                                style: TextStyle(
                                    fontSize: MySize.size24,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.color,
                                    fontWeight: FontWeight.w800),
                              ),
                            ],
                          ),
                          Stack(
                            children: [
                              Container(
                                width: MySize.size40,
                                height: MySize.size40,
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                        color: Colors.black.withAlpha(90),
                                        spreadRadius: 1,
                                        blurRadius: 3,
                                        offset: const Offset(0, 2))
                                  ],
                                  borderRadius: Shape.circular(MySize.size14),
                                  color: AppColors.primaryColor,
                                ),
                                child: InkWell(
                                  onTap: () {
                                    Navigator.pushNamed(context, notificationScreen);
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.all(MySize.size8),
                                    child: SvgPicture.asset(
                                      "assets/svg/new_icons/notification_icon.svg",
                                      height: MySize.size24,
                                      width: MySize.size24,
                                    ),
                                  ),
                                ),
                              ),
                              // Notification badge
                              Consumer(
                                builder: (context, ref, child) {
                                  final unreadCount = ref.watch(unreadNotificationsCountProvider);
                                  if (unreadCount > 0) {
                                    return Positioned(
                                      right: 0,
                                      top: 0,
                                      child: Container(
                                        padding: EdgeInsets.all(MySize.size2),
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          borderRadius: BorderRadius.circular(MySize.size8),
                                        ),
                                        constraints: BoxConstraints(
                                          minWidth: MySize.size16,
                                          minHeight: MySize.size16,
                                        ),
                                        child: Text(
                                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: MySize.size10,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              ),
                            ],
                          )
                        ],
                      ),
                      healthScoreWidget(),
                      homeCards(),
                      Text(
                        "Health Metrics",
                        style: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge!.color,
                            fontSize: MySize.size20,
                            fontWeight: FontWeight.bold),
                      ),
                      activityWidget(),
                      bloodPressureWidget(),
                      sleepWidget(),
                      bmiWidget(),
                      if (userGender != "Male")
                        menstruationWidget(),
                      diabetesWidget(),
                      kidneyWidget(),
                      thyroidWidget(),
                      liverWidget(),
                      vitaminsWidget(),
                    ]),
              ),
            ),
          )),
    );
  }

  Widget healthScoreWidget() {
    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: AppColors.primaryColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: MySize.size10,
        children: [
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Your Health Score",
                        style: TextStyle(
                            color: AppColors.white,
                            fontSize: MySize.size18,
                            fontWeight: FontWeight.bold),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "84",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size27,
                                fontWeight: FontWeight.bold),
                          ),
                          Text(
                            "/100",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size18,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      Row(
                        spacing: MySize.size5,
                        children: [
                          Text(
                            "Good",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.bold),
                          ),
                          Icon(
                            Icons.circle,
                            color: AppColors.lightGrey,
                            size: MySize.size14,
                          ),
                          Text(
                            "Improving",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      )
                    ],
                  ),
                  SvgPicture.asset(
                    "assets/svg/new_icons/health_card_icon.svg",
                    height: MySize.size90,
                    width: MySize.size90,
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(
                    top: MySize.size15,),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: Shape.circular(MySize.size10),
                    color: AppColors.white.withAlpha(50),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(MySize.size8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Complete Your Daily Checkin",
                              style: TextStyle(
                                  fontSize: MySize.size15,
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold),
                            ),
                            SizedBox(
                              child: ElevatedButton(
                                  onPressed: () {},
                                  child: Text(
                                    "Check in",
                                    style: TextStyle(
                                        fontSize: MySize.size14,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.black),
                                  )),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget activityWidget() {
    final healthData = ref.watch(healthDataProvider);
    final healthNotifier = ref.watch(healthDataProvider.notifier);

    // Get goals
    final stepGoal = healthNotifier.getDailyStepGoal();
    final distanceGoal = healthNotifier.getDailyDistanceGoal();

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, healthDetailScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.all(MySize.size4),
                  child: SvgPicture.asset(
                    "assets/svg/notes_icon.svg",
                    height: MySize.size26,
                    width: MySize.size26,
                  ),
                ),
                Space.width(8),
                Text(
                  "Activity",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
              ],
            ),
            Space.height(20),
            Row(
              children: [
                Expanded(
                  child: _buildActivityCard(
                    iconAsset: "assets/svg/distance_icon.svg",
                    title: "Distance",
                    value:
                        "${healthNotifier.getFormattedDistance()}/$distanceGoal Kms",
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: _buildActivityCard(
                    iconAsset: "assets/svg/calories_icon.svg",
                    title: "Calories",
                    value: "${healthData.calories?.toInt() ?? 0}",
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: _buildActivityCard(
                    iconAsset: "assets/svg/steps_icon.svg",
                    title: "Steps",
                    value: "${healthData.steps ?? 0}/$stepGoal Steps",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityCard({
    required String iconAsset,
    required String title,
    required String value,
  }) {
    return Column(
      children: [
        Container(
          width: MySize.size50,
          height: MySize.size50,
          decoration: BoxDecoration(
            color: AppColors.primaryColor,
            borderRadius: Shape.circular(MySize.size12),
          ),
          child: Padding(
            padding: EdgeInsets.all(MySize.size12),
            child: SvgPicture.asset(
              colorFilter: ColorFilter.mode(
                AppColors.white,
                BlendMode.srcIn,
              ),
              iconAsset,
              height: MySize.size24,
              width: MySize.size24,
            ),
          ),
        ),
        Space.height(8),
        Text(
          title,
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge!.color,
          ),
        ),
        Space.height(4),
        Text(
          value,
          style: TextStyle(
            fontSize: MySize.size12,
            color: Theme.of(context).textTheme.bodySmall!.color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget bmiWidget() {
    final bmi = ref.watch(combinedBmiProvider);
    final height = ref.watch(combinedHeightProvider);
    final weight = ref.watch(combinedWeightProvider);

    final bmiProgress = (bmi / 40.0).clamp(0.0, 1.0);

    return InkWell(
      onTap: () async {
        await Navigator.pushNamed(context, bmiScreen);
        // Refresh BMI data when returning from BMI screen
        ref.invalidate(combinedBmiProvider);
        ref.invalidate(combinedHeightProvider);
        ref.invalidate(combinedWeightProvider);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/bmi_icon.svg",
                      height: MySize.size24,
                      width: MySize.size24,
                    ),
                    Space.width(8),
                    Text(
                      "BMI",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge!.color,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodySmall!.color,
                      ),
                    ),
                    Space.width(4),
                    Icon(
                      Icons.chevron_right,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ],
                ),
              ],
            ),
            Space.height(5),
            // BMI Circle
            Center(
              child: SizedBox(
                width: MySize.size120,
                height: MySize.size120,
                child: CircularPercentIndicator(
                  radius: MySize.size60,
                  lineWidth: MySize.size8,
                  percent: bmiProgress,
                  center: Text(
                    bmi > 0 ? bmi.toStringAsFixed(1) : "0.0",
                    style: TextStyle(
                      fontSize: MySize.size24,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    ),
                  ),
                  backgroundColor: AppColors.progressBackground.withAlpha(31),
                  progressColor: AppColors.primaryColor,
                  circularStrokeCap: CircularStrokeCap.round,
                ),
              ),
            ),
            Space.height(20),
            // Height and Weight Cards
            Row(
              children: [
                Expanded(
                  child: _buildBMICard(
                    iconAsset: "assets/svg/height_icon.svg",
                    title: "Height",
                    value: height > 0 ? height.toStringAsFixed(0) : "00",
                    unit: "Cm",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildBMICard(
                    iconAsset: "assets/svg/weight_icon.svg",
                    title: "Weight",
                    value: weight > 0 ? weight.toStringAsFixed(0) : "00",
                    unit: "Kgs",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBMICard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
  }) {
    return Card(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.all(MySize.size4),
                  child: SvgPicture.asset(
                    colorFilter: ColorFilter.mode(
                      AppColors.primaryColor,
                      BlendMode.srcIn,
                    ),
                    iconAsset,
                    height: MySize.size24,
                    width: MySize.size24,
                  ),
                ),
                Space.width(8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
              ],
            ),
            Space.height(8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: MySize.size24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Space.width(8),
                Padding(
                  padding: EdgeInsets.only(bottom: MySize.size4),
                  child: Text(
                    unit,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget sleepWidget() {
    final healthData = ref.watch(healthDataProvider);

    // Calculate sleep quality percentage (assuming 8 hours is 100%)
    final sleepHours = healthData.sleepHours ?? 0;
    final sleepQualityPercent = (sleepHours / 8.0).clamp(0.0, 1.0);
    final sleepQualityDisplay = (sleepQualityPercent * 100).toInt();

    // Format sleep hours and minutes
    final sleepHoursInt = sleepHours.floor();
    final sleepMinutes = ((sleepHours - sleepHoursInt) * 60).round();

    return InkWell(
      onTap: () => Navigator.pushNamed(context, sleepAnalysisScreen),
      child: Container(
        padding: EdgeInsets.only(
          left: MySize.size15,
          right: MySize.size15,
          top: MySize.size15,
          bottom: MySize.size10,
        ),
        decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryColor.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(2, 6),
              ),
            ],
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).scaffoldBackgroundColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/new_icons/sleep_icon.svg",
                      height: MySize.size26,
                      width: MySize.size26,
                    ),
                    Space.width(10),
                    Text(
                      "Sleep Analysis",
                      style: TextStyle(
                          fontSize: MySize.size18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Row(
                  spacing: MySize.size5,
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodySmall!.color),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    )
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${sleepHours.floor()}h ${sleepMinutes}m",
                          style: TextStyle(
                              fontSize: MySize.size26,
                              fontWeight: FontWeight.w600,
                              color:
                                  Theme.of(context).textTheme.bodyLarge!.color),
                        ),
                        Text(
                          "Sleep Quality",
                          style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.w500,
                              color:
                                  Theme.of(context).textTheme.bodySmall!.color),
                        ),
                      ],
                    ),
                  ],
                ),
                Space.width(20),
                CircularPercentIndicator(
                  radius: MySize.size50,
                  lineWidth: MySize.size10,
                  percent: sleepQualityPercent,
                  center: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("$sleepQualityDisplay%",
                            style: TextStyle(
                                fontSize: MySize.size23,
                                fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                  backgroundColor: AppColors.progressBackground.withAlpha(31),
                  progressColor: AppColors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget bloodPressureWidget() {
    final latestReading = ref.watch(latestBloodPressureReadingProvider);
    final systolic = latestReading?['systolic']?.toString() ?? "00";
    final diastolic = latestReading?['diastolic']?.toString() ?? "00";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, bloodPressureScreen),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: MySize.size10,
                  children: [
                    SvgPicture.asset(
                      "assets/svg/new_icons/blood_pressure_icon.svg",
                      height: MySize.size26,
                      width: MySize.size26,
                    ),
                    Text(
                      "Blood Pressure",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Row(
                  spacing: MySize.size5,
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodySmall!.color),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    )
                  ],
                ),
              ],
            ),
            Space.height(10),
            Row(
              children: [
                Text(
                  systolic,
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Text(
                  "/",
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Text(
                  diastolic,
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Space.width(5),
                Text(
                  "mmHg",
                  style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
              ],
            ),
            Text(
              "Stable Range",
              style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodySmall!.color),
            ),
          ],
        ),
      ),
    );
  }

  Widget heartRateWidget() {
    final latestReading = ref.watch(latestBloodPressureReadingProvider);
    final heartRate = ref.watch(healthDataProvider).heartRate?.toInt() ?? 00;
    final pulseRate =
        latestReading?['pulse_rate']?.toString() ?? heartRate.toString();

    return InkWell(
      onTap: () {},
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: MySize.size10,
                  children: [
                    SvgPicture.asset(
                      "assets/svg/new_icons/heart_rate_icon.svg",
                      height: MySize.size26,
                      width: MySize.size26,
                    ),
                    Text(
                      "Heart Rate",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Row(
                  spacing: MySize.size5,
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodySmall!.color),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    )
                  ],
                ),
              ],
            ),
            Space.height(10),
            Row(
              children: [
                Text(
                  pulseRate,
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Space.width(5),
                Text(
                  "bpm",
                  style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
              ],
            ),
            Text(
              "Resting Rate",
              style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodySmall!.color),
            ),
          ],
        ),
      ),
    );
  }

  Widget diabetesWidget() {
    // Watch diabetes data from providers
    final latestHba1cValue = ref.watch(latestHba1cReadingProvider);
    final latestHba1cDate = ref.watch(latestHba1cDateProvider);
    final estimatedHba1c = ref.watch(estimatedHba1cProvider);
    final latestSugarReading = ref.watch(latestSugarReadingProvider);

    // Format HbA1c value - show 0 if no data
    final currentHba1cValue = latestHba1cValue ?? 0.0;
    final currentHba1cDisplay = currentHba1cValue > 0
        ? "${currentHba1cValue.toStringAsFixed(1)}%"
        : "0%";

    // Format estimated HbA1c - show 0 if no data
    final estimatedHba1cValue = estimatedHba1c > 0 ? estimatedHba1c : 0.0;
    final estimatedHba1cDisplay = estimatedHba1cValue > 0
        ? "${estimatedHba1cValue.toStringAsFixed(1)}%"
        : "0%";

    // Format blood sugar - show 0 if no data
    final bloodSugarValue = latestSugarReading ?? 0;

    // Determine status for current HbA1c
    String currentHba1cStatus = "No Data";
    Color currentHba1cStatusColor = Colors.grey;
    if (currentHba1cValue > 0) {
      if (currentHba1cValue < 5.7) {
        currentHba1cStatus = "Normal";
        currentHba1cStatusColor = Colors.green;
      } else if (currentHba1cValue < 6.5) {
        currentHba1cStatus = "Pre-Diabetes";
        currentHba1cStatusColor = Colors.orange;
      } else {
        currentHba1cStatus = "Diabetes";
        currentHba1cStatusColor = Colors.red;
      }
    }

    // Determine status for estimated HbA1c
    String estimatedHba1cStatus = "No Data";
    Color estimatedHba1cStatusColor = Colors.grey;
    if (estimatedHba1cValue > 0) {
      if (estimatedHba1cValue < 5.7) {
        estimatedHba1cStatus = "Normal";
        estimatedHba1cStatusColor = Colors.green;
      } else if (estimatedHba1cValue < 6.5) {
        estimatedHba1cStatus = "Pre-Diabetes";
        estimatedHba1cStatusColor = Colors.orange;
      } else {
        estimatedHba1cStatus = "Diabetes";
        estimatedHba1cStatusColor = Colors.red;
      }
    }

    // Format the date text for current HbA1c
    String dateText = "Current HbA1c";
    if (latestHba1cDate != null && currentHba1cValue > 0) {
      try {
        final parts = latestHba1cDate.split('-');
        if (parts.length == 3) {
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          final date = DateTime(year, month, day);
          final now = DateTime.now();
          final difference = now.difference(date);

          if (difference.inDays < 30) {
            dateText = "Current HbA1c Recent";
          } else if (difference.inDays < 60) {
            dateText = "Current HbA1c 1 Month ago";
          } else {
            final months = (difference.inDays / 30).floor();
            dateText = "Current HbA1c $months Months ago";
          }
        }
      } catch (e) {
        dateText = "Current HbA1c";
      }
    }

    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        borderRadius: Shape.circular(MySize.size10),
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    "assets/svg/diabetes_icon.svg",
                    height: MySize.size24,
                    width: MySize.size24,
                  ),
                  Space.width(8),
                  Text(
                    "Diabetes Management",
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    "Today",
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ),
                  Space.width(4),
                  Icon(
                    Icons.chevron_right,
                    size: MySize.size16,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ],
              ),
            ],
          ),
          Space.height(20),

          // Current HbA1c Card
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, '/hba1cScreen');
            },
            child: _buildDiabetesCard(
              iconAsset: "assets/svg/diabetes_icon.svg",
              title: dateText,
              value: currentHba1cDisplay,
              status: currentHba1cStatus,
              statusColor: currentHba1cStatusColor,
            ),
          ),

          Space.height(15),

          // Estimated HbA1c Card
          _buildDiabetesCard(
            iconAsset: "assets/svg/diabetes_icon.svg",
            title: "Estimated HbA1c",
            value: estimatedHba1cDisplay,
            status: estimatedHba1cStatus,
            statusColor: estimatedHba1cStatusColor,
          ),

          Space.height(15),

          // Blood Sugar Card
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, '/diabetesScreen');
            },
            child: _buildDiabetesCard(
              iconAsset: "assets/svg/blood_sugar_icon.svg",
              title: "Blood Sugar",
              value: bloodSugarValue.toString(),
              unit: "mg/dl",
              status: "",
              statusColor: AppColors.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiabetesCard({
    required String iconAsset,
    required String title,
    required String value,
    String? unit,
    required String status,
    required Color statusColor,
  }) {
    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: Shape.circular(MySize.size10),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: MySize.size40,
            height: MySize.size40,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: Shape.circular(MySize.size8),
            ),
            child: Center(
              child: SvgPicture.asset(
                iconAsset,
                width: MySize.size24,
                height: MySize.size24,
                colorFilter: ColorFilter.mode(
                  AppColors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),

          Space.width(15),

          // Title
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: MySize.size14,
                color: Theme.of(context).textTheme.bodySmall!.color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Value and Status
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: MySize.size20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    ),
                  ),
                  if (unit != null) ...[
                    Space.height(4),
                    Text(
                      unit,
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
              if (status.isNotEmpty) ...[
                Space.height(4),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: statusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget kidneyWidget() {
    // Get the latest kidney readings from the provider
    final latestReadings = ref.watch(latestKidneyReadingsProvider);

    // Extract values and show 0 if no readings exist
    final gfr = latestReadings?['gfr']?.toString() ?? "0";
    final creatinine = latestReadings?['creatinine']?.toString() ?? "0";
    final bun = latestReadings?['bun']?.toString() ?? "0";
    final albumin = latestReadings?['albumin']?.toString() ?? "0";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, kidneyManagementScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with kidney icon and title
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/kidney_icon.svg",
                      height: MySize.size24,
                      width: MySize.size24,
                      colorFilter: ColorFilter.mode(
                        Colors.red,
                        BlendMode.srcIn,
                      ),
                    ),
                    Space.width(8),
                    Text(
                      "Kidney Management",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge!.color,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodySmall!.color,
                      ),
                    ),
                    Space.width(4),
                    Icon(
                      Icons.chevron_right,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ],
                ),
              ],
            ),
            Space.height(20),

            // First row: GFR Rate and Creatinine
            Row(
              children: [
                Expanded(
                  child: _buildKidneyCard(
                    iconAsset: "assets/svg/gfr_icon.svg",
                    title: "GFR Rate",
                    value: gfr,
                    unit: "U/L",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildKidneyCard(
                    iconAsset: "assets/svg/creatinine_icon.svg",
                    title: "Creatinine",
                    value: creatinine,
                    unit: "mg/dl",
                  ),
                ),
              ],
            ),

            Space.height(15),

            // Second row: BUN and Albumin
            Row(
              children: [
                Expanded(
                  child: _buildKidneyCard(
                    iconAsset: "assets/svg/bun_icon.svg",
                    title: "Bun",
                    value: bun,
                    unit: "mg/dl",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildKidneyCard(
                    iconAsset: "assets/svg/albumin_icon.svg",
                    title: "Albumin",
                    value: albumin,
                    unit: "U/L",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKidneyCard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
  }) {
    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: Shape.circular(MySize.size10),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and title row
          Row(
            children: [
              Container(
                width: MySize.size40,
                height: MySize.size40,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: SvgPicture.asset(
                    iconAsset,
                    width: MySize.size20,
                    height: MySize.size20,
                    colorFilter: ColorFilter.mode(
                      AppColors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              Space.width(10),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          Space.height(15),

          // Value and unit
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.bodyLarge!.color,
                ),
              ),
              Space.width(4),
              Padding(
                padding: EdgeInsets.only(bottom: MySize.size2),
                child: Text(
                  unit,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget thyroidWidget() {
    // Get the latest thyroid reading from the provider
    final latestReading = ref.watch(latestThyroidReadingProvider);

    // Extract values and show 0 if no readings exist
    final tsh = latestReading?['tsh']?.toString() ?? "0";
    final t3 = latestReading?['t3']?.toString() ?? "0";
    final t4 = latestReading?['t4']?.toString() ?? "0";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, thyroidManagementScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/thyroid_icon.svg",
                      height: MySize.size24,
                      width: MySize.size24,
                    ),
                    Space.width(8),
                    Text(
                      "Thyroid Management",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge!.color,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodySmall!.color,
                      ),
                    ),
                    Space.width(4),
                    Icon(
                      Icons.chevron_right,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ],
                ),
              ],
            ),

            Space.height(20),

            // Thyroid metrics in a row
            Row(
              children: [
                Expanded(
                  child: _buildThyroidCard(
                    iconAsset: "assets/svg/tsh_icon.svg",
                    title: "TSH",
                    value: tsh,
                    unit: "mIU/L",
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: _buildThyroidCard(
                    iconAsset: "assets/svg/freet3_icon.svg",
                    title: "Free T3",
                    value: t3,
                    unit: "pg/mL",
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: _buildThyroidCard(
                    iconAsset: "assets/svg/freet4_icon.svg",
                    title: "Free T4",
                    value: t4,
                    unit: "ng/dL",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThyroidCard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
  }) {
    return Container(
      padding: EdgeInsets.all(MySize.size12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: Shape.circular(MySize.size10),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Icon
          Row(
            children: [
              Container(
                width: MySize.size36,
                height: MySize.size36,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: SvgPicture.asset(
                    iconAsset,
                    width: MySize.size20,
                    height: MySize.size20,
                    colorFilter: ColorFilter.mode(
                      AppColors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              Space.width(8),
              // Title
              Text(
                title,
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: Theme.of(context).textTheme.bodySmall!.color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          Space.height(20),

          // Value
          Text(
            value,
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyLarge!.color,
            ),
          ),

          Space.height(2),

          // Unit
          Text(
            unit,
            style: TextStyle(
              fontSize: MySize.size10,
              color: AppColors.primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget liverWidget() {
    // Get the latest liver readings from the provider
    final latestReading = ref.watch(latestLiverReadingProvider);

    // Extract values and show 0 if no readings exist
    final ast = latestReading?['ast']?.toString() ?? "0";
    final alt = latestReading?['alt']?.toString() ?? "0";
    final bilirubin = latestReading?['bilirubin']?.toString() ?? "0";
    final alp = latestReading?['alp']?.toString() ?? "0";

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, liverScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with liver icon and title
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/liver_icon.svg",
                      height: MySize.size24,
                      width: MySize.size24,
                      colorFilter: ColorFilter.mode(
                        AppColors.primaryColor,
                        BlendMode.srcIn,
                      ),
                    ),
                    Space.width(8),
                    Text(
                      "Liver Management",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge!.color,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodySmall!.color,
                      ),
                    ),
                    Space.width(4),
                    Icon(
                      Icons.chevron_right,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ],
                ),
              ],
            ),
            Space.height(20),

            // First row: AST and ALT
            Row(
              children: [
                Expanded(
                  child: _buildLiverCard(
                    iconAsset: "assets/svg/ast_icon.svg",
                    title: "Aspartate Aminotransferase",
                    value: ast,
                    unit: "U/L",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildLiverCard(
                    iconAsset: "assets/svg/ame_icon.svg",
                    title: "Alanine Aminotransferase",
                    value: alt,
                    unit: "mg/dl",
                  ),
                ),
              ],
            ),

            Space.height(15),

            // Second row: Bilirubin and Alkaline Phosphatase
            Row(
              children: [
                Expanded(
                  child: _buildLiverCard(
                    iconAsset: "assets/svg/bilirubin_icon.svg",
                    title: "Bilirubin Levels",
                    value: bilirubin,
                    unit: "mg/dl",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildLiverCard(
                    iconAsset: "assets/svg/alkaline_icon.svg",
                    title: "Alkaline Phosphatase",
                    value: alp,
                    unit: "U/L",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiverCard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
  }) {
    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: Shape.circular(MySize.size10),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and title row
          Row(
            children: [
              Container(
                width: MySize.size36,
                height: MySize.size36,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: SvgPicture.asset(
                    iconAsset,
                    width: MySize.size20,
                    height: MySize.size20,
                    colorFilter: ColorFilter.mode(
                      AppColors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              Space.width(10),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          Space.height(15),

          // Value and unit
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.bodyLarge!.color,
                ),
              ),
              Space.width(4),
              Padding(
                padding: EdgeInsets.only(bottom: MySize.size2),
                child: Text(
                  unit,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget menstruationWidget() {
    // Watch period data from providers
    final periodData = ref.watch(periodProvider);
    final nextPeriod = ref.watch(nextPeriodProvider);

    // Extract cycle length and period duration from the latest data
    int cycleLength = 0;
    int periodDuration = 0;
    String cycleStatus = "No Data";
    String periodStatus = "No Data";

    // Get data from period history
    if (periodData.isNotEmpty) {
      // Get the latest period data
      final sortedKeys = periodData.keys.toList()
        ..sort((a, b) {
          final dateA = DateFormat('d-M-yyyy').parse(a);
          final dateB = DateFormat('d-M-yyyy').parse(b);
          return dateB.compareTo(dateA);
        });

      if (sortedKeys.isNotEmpty) {
        final latestData = periodData[sortedKeys.first];
        if (latestData != null && latestData['readings'] != null) {
          final readings = latestData['readings'] as List;
          if (readings.isNotEmpty) {
            final reading = readings[0];
            cycleLength = reading['cycle_length'] ?? 0;
            periodDuration = reading['period_duration'] ?? 0;
          }
        }
      }
    }

    // Use next period data if available
    if (nextPeriod?.cycleLength != null) {
      cycleLength = nextPeriod!.cycleLength!;
    }

    // Determine cycle status based on cycle length (only if we have data)
    if (cycleLength > 0) {
      if (cycleLength >= 21 && cycleLength <= 35) {
        cycleStatus = "Normal";
      } else {
        cycleStatus = "Irregular";
      }
    }

    // Determine period status based on period duration (only if we have data)
    if (periodDuration > 0) {
      if (periodDuration >= 3 && periodDuration <= 7) {
        periodStatus = "Normal";
      } else {
        periodStatus = "Irregular";
      }
    }

    // For Period Status card, show period duration
    int displayValue = periodDuration;
    String statusText = periodStatus;

    return InkWell(
      onTap: () async {
        try {
          final firestoreService = FirestoreService();
          final hasData = await firestoreService.hasPeriodData();

          if (mounted) {
            if (hasData) {
              // If period data exists, navigate directly to period tracker screen
              Navigator.pushNamed(context, periodTrackerScreen);
            } else {
              // If no data exists, navigate to onboarding screen
              Navigator.pushNamed(context, onboardingScreen);
            }
          }
        } catch (e) {
          log('Error checking period data: $e');
          // If there's an error, default to onboarding screen
          if (mounted) {
            Navigator.pushNamed(context, onboardingScreen);
          }
        }
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size10),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/menstrual_icon.svg",
                      height: MySize.size24,
                      width: MySize.size24,
                    ),
                    Space.width(8),
                    Text(
                      "Menstrual Cycle",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge!.color,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodySmall!.color,
                      ),
                    ),
                    Space.width(4),
                    Icon(
                      Icons.chevron_right,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ],
                ),
              ],
            ),
            Space.height(20),
            // Period Length and Period Status Cards
            Row(
              children: [
                Expanded(
                  child: _buildMenstrualCard(
                    iconAsset: "assets/svg/period_length_icon.svg",
                    title: "Period Length",
                    value: cycleLength.toString(),
                    unit: "Days",
                    status: cycleStatus,
                    statusColor: cycleStatus == "Normal"
                        ? AppColors.primaryColor
                        : cycleStatus == "No Data"
                            ? Colors.grey
                            : Colors.orange,
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildMenstrualCard(
                    iconAsset: "assets/svg/period_status_icon.svg",
                    title: "Period Status",
                    value: displayValue.toString(),
                    unit: "Days",
                    status: statusText,
                    statusColor: periodStatus == "Normal"
                        ? AppColors.primaryColor
                        : periodStatus == "No Data"
                            ? Colors.grey
                            : Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenstrualCard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
    required String status,
    required Color statusColor,
  }) {
    return Card(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Padding(
        padding: EdgeInsets.all(MySize.size8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon and title row
            Row(
              children: [
                Container(
                  width: MySize.size40,
                  height: MySize.size40,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: Shape.circular(MySize.size20),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(MySize.size8),
                    child: SvgPicture.asset(
                      iconAsset,
                      height: MySize.size24,
                      width: MySize.size24,
                    ),
                  ),
                ),
                Space.width(12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
              ],
            ),
            Space.height(12),
            // Value and unit row
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: MySize.size28,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Space.width(8),
                Padding(
                  padding: EdgeInsets.only(bottom: MySize.size6),
                  child: Text(
                    unit,
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ),
                ),
              ],
            ),
            Space.height(4),
            // Status
            Text(
              status,
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: statusColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget vitaminsWidget() {
    // Get vitamin intake and mineral data from providers
    final vitaminIntake = ref.watch(vitaminIntakeProvider);
    final latestVitaminReading = ref.watch(latestVitaminReadingProvider);

    // Extract vitamin intake values and show 0 if no data exists
    final vitaminsTaken = vitaminIntake['vitaminsTaken']?.toString() ?? "0";
    final totalVitamins = vitaminIntake['totalVitamins']?.toString() ?? "5";

    // Extract mineral values and show 0 if no readings exist
    final calcium = latestVitaminReading?['calcium']?.toString() ?? "0";
    final magnesium = latestVitaminReading?['magnesium']?.toString() ?? "0";
    final phosphorus = latestVitaminReading?['phosphorus']?.toString() ?? "0";

    return InkWell(
      onTap: () {
        // Navigate to vitamins screen when implemented
        // Navigator.pushNamed(context, vitaminsScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Vitamins & Minerals",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodySmall!.color,
                      ),
                    ),
                    Space.width(4),
                    Icon(
                      Icons.chevron_right,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ],
                ),
              ],
            ),

            Space.height(20),

            // First row: Vitamin intake tracking
            Row(
              children: [
                Expanded(
                  child: _buildVitaminCard(
                    iconAsset: "assets/svg/vitamin_taken_icon.svg",
                    title: "Vitamin\nTaken",
                    value: "$vitaminsTaken/$totalVitamins",
                    unit: "",
                    isCircularProgress: true,
                    progress: double.tryParse(vitaminsTaken) ?? 0,
                    total: double.tryParse(totalVitamins) ?? 5,
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildVitaminCard(
                    iconAsset: "assets/svg/time_icon.svg",
                    title: "Next\nScheduled",
                    value: "",
                    unit: "",
                    showValueAndUnit: false,
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildVitaminCard(
                    iconAsset: "assets/svg/missed_intake_icon.svg",
                    title: "Missed\nIntake",
                    value: "",
                    unit: "",
                    showValueAndUnit: false,
                  ),
                ),
              ],
            ),

            Space.height(15),

            // Second row: Mineral levels
            Row(
              children: [
                Expanded(
                  child: _buildVitaminCard(
                    iconAsset: "assets/svg/calcium_icon.svg",
                    title: "Calcium",
                    value: calcium,
                    unit: "mg/dl",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildVitaminCard(
                    iconAsset: "assets/svg/magnesium_icon.svg",
                    title: "Magnesium",
                    value: magnesium,
                    unit: "mg/dl",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildVitaminCard(
                    iconAsset: "assets/svg/phosphorus_icon.svg",
                    title: "Phosphorus",
                    value: phosphorus,
                    unit: "mg/dl",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVitaminCard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
    bool isCircularProgress = false,
    bool showValueAndUnit = true,
    double progress = 0,
    double total = 5,
  }) {
    return Container(
      padding: EdgeInsets.all(MySize.size12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: Shape.circular(MySize.size10),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Icon with circular progress for vitamin taken
          if (isCircularProgress)
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: MySize.size40,
                  height: MySize.size40,
                  child: CircularProgressIndicator(
                    value: total > 0 ? progress / total : 0,
                    strokeWidth: 3,
                    backgroundColor:
                        Theme.of(context).dividerColor.withValues(alpha: 0.3),
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                  ),
                ),
                // Value inside the circular progress
                Text(
                  value,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            )
          else
            Container(
              width: MySize.size40,
              height: MySize.size40,
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SvgPicture.asset(
                  iconAsset,
                  width: MySize.size20,
                  height: MySize.size20,
                  colorFilter: ColorFilter.mode(
                    AppColors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),

          Space.height(8),

          // Value and unit (only show for mineral cards)
          if (showValueAndUnit && !isCircularProgress) ...[
            Text(
              value,
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge!.color,
              ),
              textAlign: TextAlign.center,
            ),
            if (unit.isNotEmpty) ...[
              Space.height(2),
              Text(
                unit,
                style: TextStyle(
                  fontSize: MySize.size10,
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            Space.height(4),
          ] else if (!showValueAndUnit) ...[
            // Add some spacing for cards without values
            Space.height(20),
          ] else if (isCircularProgress) ...[
            // Add spacing for circular progress card to match other cards height
            Space.height(24),
          ],

          // Title
          Text(
            title,
            style: TextStyle(
              fontSize: MySize.size10,
              color: Theme.of(context).textTheme.bodySmall!.color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget homeCards() {
    // Watch providers for dynamic data
    final glassesToday = ref.watch(glassesTodayProvider);
    final glassesPerDay = ref.watch(glassesPerDayProvider);
    final reportState = ref.watch(reportListProvider);

    // Calculate reports count
    final reportsCount = reportState.when(
      data: (reports) => reports.length,
      loading: () => 0,
      error: (_, __) => 0,
    );

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: MySize.size15,
      crossAxisSpacing: MySize.size15,
      childAspectRatio: 2.5,
      children: [
        _buildHomeCard(
          icon: Icons.medication,
          title: "Medication",
          subtitle: "Add or Upload",
          onTap: () {
            // Navigate to setup screen only if no medications exist
            if (_hasMedications()) {
              Navigator.pushNamed(context, medicationListScreen);
            } else {
              Navigator.pushNamed(context, setupMedicationScreen);
            }
          },
        ),
        _buildHomeCard(
          icon: Icons.description,
          title: "Reports",
          subtitle: "$reportsCount Reports",
          onTap: () => Navigator.pushNamed(context, reportScreen),
        ),
        _buildHomeCard(
          icon: Icons.local_drink,
          title: "Water Intake",
          subtitle: "$glassesToday/$glassesPerDay Glasses",
          onTap: () => Navigator.pushNamed(context, waterInTakeScreen),
        ),
        _buildHomeCard(
          icon: Icons.calendar_today,
          title: "Book A Test",
          subtitle: "At Home",
          onTap: () => Navigator.pushNamed(context, bookTestScreen),
        ),
      ],
    );
  }

  Widget _buildHomeCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: Shape.circular(MySize.size12),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size12),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: MySize.size50,
              height: MySize.size50,
              decoration: BoxDecoration(
                borderRadius: Shape.circular(MySize.size12),
                color: AppColors.primaryColor,
              ),
              child: Icon(
                icon,
                color: AppColors.white,
                size: MySize.size24,
              ),
            ),
            Space.width(12),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Space.height(4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  bool _hasMedications() {
    final dailyMedications = ref.read(medicationProvider);
    final weeklyMedications = ref.read(weeklyMedicationProvider);
    final monthlyMedications = ref.read(monthlyMedicationProvider);

    return dailyMedications.isNotEmpty ||
        weeklyMedications.isNotEmpty ||
        monthlyMedications.isNotEmpty;
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Morning';
    } else if (hour < 17) {
      return 'Afternoon';
    } else {
      return 'Evening';
    }
  }
}
