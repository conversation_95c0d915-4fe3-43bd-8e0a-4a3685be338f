import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/utils/snackbar.dart';
import 'package:healo/common/widgets/bmi_recommendation_cards.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/next_period_widget.dart';
import 'package:healo/common/widgets/period_chart.dart';
import 'package:healo/common/widgets/period_graph.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/period_provider.dart';
import 'dart:developer';
import 'package:intl/intl.dart';

class PeriodTrackerScreen extends ConsumerStatefulWidget {
  const PeriodTrackerScreen({super.key});

  @override
  ConsumerState<PeriodTrackerScreen> createState() =>
      _PeriodTrackerScreenState();
}

class _PeriodTrackerScreenState extends ConsumerState<PeriodTrackerScreen> {
  DateTime? _selectedDate;
  bool switchValue = false;
  TextEditingController periodDateController = TextEditingController();
  TextEditingController cycleLengthController = TextEditingController();
  TextEditingController durationController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    String intiDate =
        "${_selectedDate!.day}-${_selectedDate!.month}-${_selectedDate!.year}";
    ref.read(periodProvider.notifier).fetchPeriodHistory();
    ref.read(periodPhaseProvider.notifier).determinePhaseForDate(intiDate);
    ref.read(recentPeriodHistoryProvider.notifier).fetchRecentHistory();
    ref.read(nextPeriodProvider.notifier).determineNextPeriod(
          _selectedDate!,
          isLogging: false,
        );
    ref.read(nextPeriodPredictionProvider.notifier).refreshPrediction();
  }

  @override
  Widget build(BuildContext context) {
    final periodHistory = ref.watch(periodProvider);
    final today = DateTime.now();
    final dateRange = List.generate(5, (i) {
      return today.subtract(Duration(days: 4 - i));
    });
    final phases = ref.watch(periodPhaseProvider);
    log("phases: ${phases?.nextPeriodDate}");

    Future<void> pickDate(Function(String) onDatePicked) async {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime(2000),
        lastDate: DateTime.now(),
      );
      if (picked != null) {
        final selectedDate = DateFormat('dd-MM-yyyy').format(picked);
        ref
            .read(periodPhaseProvider.notifier)
            .determinePhaseForDate(selectedDate);

        onDatePicked(selectedDate);
      }
    }

    void showLogPeriodDialog(BuildContext context) {
      String selectedFlow = "Light";
      periodDateController.text = "";
      showDialog(
          context: context,
          builder: (context) {
            return StatefulBuilder(builder: (context, setState) {
              return Consumer(
                builder: (context, ref, child) {
                  final phaseState = ref.watch(periodPhaseProvider);
                  bool dateExists = phaseState != null;

                  return AlertDialog(
                    backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.size16),
                    ),
                    contentPadding: EdgeInsets.all(MySize.size20),
                    content: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "Log your menstrual cycle",
                            style: TextStyle(
                              fontSize: MySize.size17,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Space.height(20),
                          Text(
                            "Period date",
                            style: TextStyle(
                              fontSize: MySize.size14,
                            ),
                          ),
                          Space.height(MySize.size4),
                          TextField(
                            controller: periodDateController,
                            readOnly: true,
                            decoration: InputDecoration(
                              hintText: "Enter your Start Date",
                              suffixIcon: IconButton(
                                icon: Icon(Icons.calendar_today,
                                    color: AppColors.primaryColor),
                                onPressed: () async {
                                  await pickDate((pickedDate) {
                                    // Update local state and controller text here
                                    setState(() {
                                      periodDateController.text = pickedDate;
                                    });
                                  });
                                },
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide:
                                    BorderSide(color: AppColors.primaryColor),
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                          Space.height(MySize.size20),
                          if (!dateExists &&
                              periodDateController.text.trim().isNotEmpty) ...[
                            Row(
                              children: [
                                Icon(Icons.warning_amber_rounded,
                                    size: 20, color: AppColors.red),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    "No previous data found. Please enter your period details to begin tracking",
                                    style: TextStyle(
                                      fontSize: MySize.size13,
                                      color: AppColors.primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            Space.height(MySize.size20),
                            Text("Cycle Length",
                                style: TextStyle(fontSize: MySize.size14)),
                            Space.height(MySize.size4),
                            TextField(
                              controller: cycleLengthController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                hintText: "Enter Cycle Length",
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                            Space.height(MySize.size20),
                          ],
                          Space.height(MySize.size20),
                          Text("Period Duration",
                              style: TextStyle(fontSize: MySize.size14)),
                          Space.height(MySize.size4),
                          TextField(
                            controller: durationController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              hintText: "Enter Period Duration",
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                          Space.height(MySize.size20),
                          Text(
                            "How would you describe your flow today?",
                            style: TextStyle(fontSize: MySize.size14),
                          ),
                          Space.height(MySize.size10),
                          Row(
                            children: [
                              Expanded(
                                child: buildFlowOption(
                                  context,
                                  title: "Light",
                                  iconCount: 1,
                                  isSelected: selectedFlow == "Light",
                                  onTap: () =>
                                      setState(() => selectedFlow = "Light"),
                                ),
                              ),
                              SizedBox(width: MySize.size10),
                              Expanded(
                                child: buildFlowOption(
                                  context,
                                  title: "Medium",
                                  iconCount: 2,
                                  isSelected: selectedFlow == "Medium",
                                  onTap: () =>
                                      setState(() => selectedFlow = "Medium"),
                                ),
                              ),
                            ],
                          ),
                          Space.height(MySize.size10),
                          buildFlowOption(
                            context,
                            title: "Heavy",
                            fixedWidth: MySize.size160,
                            iconCount: 3,
                            isSelected: selectedFlow == "Heavy",
                            onTap: () => setState(() => selectedFlow = "Heavy"),
                          ),
                          Space.height(MySize.size25),
                          Column(
                            children: [
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: () async {
                                    if (periodDateController.text
                                        .trim()
                                        .isEmpty) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                              "Please enter a valid period date"),
                                        ),
                                      );
                                      return;
                                    }

                                    try {
                                      if (phaseState == null) {
                                        await ref
                                            .read(periodProvider.notifier)
                                            .addPeriodData(
                                              lastPeriodDateStr:
                                                  periodDateController.text
                                                      .trim(),
                                              cycleLength: int.parse(
                                                  cycleLengthController.text
                                                      .trim()),
                                              periodDuration: int.parse(
                                                  durationController.text
                                                              .trim() ==
                                                          ""
                                                      ? "5"
                                                      : durationController.text
                                                          .trim()),
                                            );
                                      } else {
                                        await ref
                                            .read(periodProvider.notifier)
                                            .addPeriodData(
                                              lastPeriodDateStr:
                                                  periodDateController.text
                                                      .trim(),
                                              cycleLength: phaseState.cycleDay!,
                                              periodDuration: int.parse(
                                                  durationController.text
                                                              .trim() ==
                                                          ""
                                                      ? "5"
                                                      : durationController.text
                                                          .trim()),
                                            );
                                      }

                                      await ref
                                          .read(flowProvider.notifier)
                                          .addFlow(selectedFlow,
                                              periodDateController.text.trim());

                                      await ref
                                          .read(periodProvider.notifier)
                                          .fetchPeriodHistory();

                                      await ref
                                          .read(periodPhaseProvider.notifier)
                                          .determinePhaseForDate(
                                              "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}");
                                      _selectedDate = DateTime.now();

                                      customSnackBar(context,
                                          "Period data added successfully",
                                          color: const Color.fromARGB(
                                              255, 48, 212, 97));
                                      Navigator.pop(context);
                                    } catch (e) {
                                      customSnackBar(context,
                                          "Failed to add period data: $e",
                                          color: AppColors.red);
                                    }
                                  },
                                  style: OutlinedButton.styleFrom(
                                    side: BorderSide(
                                        color: AppColors.primaryColor),
                                    shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(MySize.size8),
                                    ),
                                    backgroundColor: AppColors.primaryColor,
                                  ),
                                  child: Text(
                                    "Save",
                                    style: TextStyle(
                                        color: AppColors.white,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              Space.height(MySize.size5),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  style: OutlinedButton.styleFrom(
                                    side: BorderSide(
                                        color: AppColors.primaryColor),
                                    shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(MySize.size8),
                                    ),
                                    backgroundColor:
                                        Theme.of(context).cardColor,
                                  ),
                                  child: Text(
                                    "Cancel",
                                    style: TextStyle(
                                      color: AppColors.primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            });
          });
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: "Period Tracker",
        actions: [
          TextButton(
            onPressed: () {
              showLogPeriodDialog(context);
            },
            child: Text(
              "Log period",
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: MySize.size16,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.size15),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _monthName(_selectedDate!.month),
                    style: TextStyle(
                      fontSize: MySize.size22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.calendar_today,
                        color: AppColors.primaryColor),
                    onPressed: () async {
                      final pickedDate = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate ?? DateTime.now(),
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );

                      if (pickedDate != null) {
                        setState(() {
                          _selectedDate = pickedDate;
                          ref
                              .read(periodPhaseProvider.notifier)
                              .determinePhaseForDate(
                                  "${pickedDate.day}-${pickedDate.month}-${pickedDate.year}");
                        });
                      }
                    },
                  ),
                ],
              ),
              SizedBox(
                height: MySize.size74,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: dateRange.map((date) {
                    return Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedDate = date;
                            ref
                                .read(periodPhaseProvider.notifier)
                                .determinePhaseForDate(
                                    "${date.day}-${date.month}-${date.year}");
                          });
                        },
                        child: Container(
                          margin:
                              EdgeInsets.symmetric(horizontal: MySize.size5),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.primaryColor),
                            color: _isSameDate(date, _selectedDate!)
                                ? AppColors.primaryColor
                                : Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(MySize.size20),
                          ),
                          padding:
                              EdgeInsets.symmetric(vertical: MySize.size12),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                _weekdayShort(date),
                                style: TextStyle(
                                  color: _isSameDate(date, _selectedDate!)
                                      ? AppColors.white
                                      : Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                  fontSize: MySize.size12,
                                ),
                              ),
                              Text(
                                "${date.day}",
                                style: TextStyle(
                                  color: _isSameDate(date, _selectedDate!)
                                      ? AppColors.white
                                      : Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              Space.height(20),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(MySize.size20),
                  ),
                  child: PeriodChart(),
                ),
              ),
              Space.height(20),
              const NextPeriodWidget(),
              Space.height(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                spacing: MySize.size5,
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        showSymptomSelectionDialog(context, ref);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(MySize.size15),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(MySize.size15),
                          child: Row(
                            children: [
                              Container(
                                margin: EdgeInsets.only(right: MySize.size8),
                                padding: EdgeInsets.all(MySize.size8),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                child: SvgPicture.asset(
                                  "assets/svg/symptoms_icon.svg",
                                  colorFilter: ColorFilter.mode(
                                    AppColors.white,
                                    BlendMode.srcIn,
                                  ),
                                  height: MySize.size22,
                                  width: MySize.size22,
                                ),
                              ),
                              Text(
                                "Log Symptoms",
                                style: TextStyle(
                                  fontSize: MySize.size14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        showMoodSelectionDialog(context, ref);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(MySize.size15),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(MySize.size15),
                          child: Row(
                            children: [
                              Container(
                                margin: EdgeInsets.only(right: MySize.size8),
                                padding: EdgeInsets.all(MySize.size8),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                child: SvgPicture.asset(
                                  "assets/svg/mood_icon.svg",
                                  colorFilter: ColorFilter.mode(
                                    AppColors.white,
                                    BlendMode.srcIn,
                                  ),
                                  height: MySize.size22,
                                  width: MySize.size22,
                                ),
                              ),
                              Text(
                                "Log Mood",
                                style: TextStyle(
                                  fontSize: MySize.size14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Space.height(20),
              Row(
                children: [
                  Text(
                    "Period History",
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Space.height(20),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(MySize.size15),
                ),
                child: Padding(
                  padding: EdgeInsets.all(MySize.size15),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            "Last 3 Months",
                            style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Space.height(20),
                      _periodHistory(),
                    ],
                  ),
                ),
              ),
              Space.height(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "Cycle Trends",
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Space.height(20),
              AnimatedToggleSwitch<bool>.size(
                current: switchValue,
                values: const [false, true],
                iconOpacity: 0.2,
                inactiveOpacity: 1.0,
                indicatorSize: Size.fromWidth(120),
                customIconBuilder: (context, local, global) => Text(
                  local.value ? 'Yearly' : 'Monthly',
                  style: TextStyle(
                    fontSize: MySize.size17,
                    fontWeight: FontWeight.w700,
                    color: Color.lerp(
                        Theme.of(context).textTheme.bodySmall?.color,
                        AppColors.backgroundColor,
                        local.animationValue),
                  ),
                ),
                borderWidth: 1,
                iconAnimationType: AnimationType.onHover,
                style: ToggleStyle(
                  borderColor: AppColors.textGray,
                  indicatorColor: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(MySize.size30),
                  indicatorBorderRadius: BorderRadius.circular(MySize.size20),
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                ),
                selectedIconScale: 1,
                onChanged: (value) => setState(() => switchValue = value),
              ),
              Space.height(20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(MySize.size24),
                  color: Theme.of(context).cardColor,
                ),
                width: double.infinity,
                height: MediaQuery.of(context).size.height / 2.4,
                child: Padding(
                  padding: EdgeInsets.all(MySize.size24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Space.height(10),
                      Expanded(
                        child: switchValue
                            ? periodHistory.isNotEmpty
                                ? PeriodGraph(
                                    data: periodHistory,
                                    isYearly: switchValue,
                                  )
                                : const Center(child: Text('No data available'))
                            : periodHistory.isNotEmpty
                                ? PeriodGraph(
                                    data: periodHistory,
                                    isYearly: switchValue,
                                  )
                                : const Center(
                                    child: Text('No data available')),
                      ),
                    ],
                  ),
                ),
              ),
              Space.height(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "Recommendations",
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Space.height(20),
              Container(
                padding: EdgeInsets.all(MySize.size15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(MySize.size10),
                  color: Theme.of(context).scaffoldBackgroundColor,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      spacing: MySize.size10,
                      //runSpacing: MySize.size10,
                      children: [
                        BMIRecommendationCards(
                          title: "Drink more water, cramps are common on Day 1",
                          value:
                              "Display the latest blood pressure readings with color coding, trends over time, and personalized insights for better management.",
                          icon: "assets/svg/glass_icon.svg",
                        ),
                        BMIRecommendationCards(
                          title: "High energy detected—good time for workouts",
                          value:
                              "High energy detected  it's a great time for a workout to support healthy blood pressure, improve circulation, and boost overall heart health",
                          icon: "assets/svg/walk_icon.svg",
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Space.height(20),
            ],
          ),
        ),
      ),
    );
  }

  void showSymptomSelectionDialog(BuildContext context, WidgetRef ref) async {
    List<String> symptoms = [
      "Headache",
      "Acne",
      "Cramps",
      "Backache",
      "Spotting",
      "Nausea",
      "Dry Mouth",
      "Fatigue"
    ];

    final Map<String, String> symptomIcons = {
      "Headache": "assets/svg/headache_symptom_icon.svg",
      "Acne": "assets/svg/acne_symptom_icon.svg",
      "Cramps": "assets/svg/cramps_symptom_icon.svg",
      "Backache": "assets/svg/backache_symptom_icon.svg",
      "Spotting": "assets/svg/spotting_symptom_icon.svg",
      "Nausea": "assets/svg/nausea_symptom_icon.svg",
      "Dry Mouth": "assets/svg/dry_mouth_symptom_icon.svg",
      "Fatigue": "assets/svg/fatigue_symptom_icon.svg",
    };

    final todaySymptoms =
        await ref.read(symptomsProvider.notifier).fetchTodaySymptoms();
    Set<String> selectedSymptoms = {...todaySymptoms};

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              backgroundColor: Theme.of(context).cardColor,
              contentPadding: const EdgeInsets.all(20),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text("How Are You Feeling?",
                            style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            )),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 240,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(2, (colIndex) {
                        return Expanded(
                          child: Column(
                            children: List.generate(4, (rowIndex) {
                              int index = colIndex * 4 + rowIndex;
                              final symptom = symptoms[index];
                              final isSelected =
                                  selectedSymptoms.contains(symptom);

                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 6),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      if (isSelected) {
                                        selectedSymptoms.remove(symptom);
                                      } else {
                                        selectedSymptoms.add(symptom);
                                      }
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5),
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? AppColors.primaryColor
                                          : Theme.of(context).cardColor,
                                      border: Border.all(
                                        color: AppColors.primaryColor,
                                      ),
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                          symptomIcons[symptom]!,
                                          height: 20,
                                          width: 20,
                                          colorFilter: ColorFilter.mode(
                                            isSelected
                                                ? AppColors.white
                                                : AppColors.primaryColor,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            symptom,
                                            textAlign: TextAlign.left,
                                            style: TextStyle(
                                              color: isSelected
                                                  ? Colors.white
                                                  : Theme.of(context)
                                                      .textTheme
                                                      .bodyLarge
                                                      ?.color,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ),
                        );
                      }),
                    ),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                        backgroundColor: AppColors.primaryColor,
                      ),
                      onPressed: () {
                        ref
                            .read(symptomsProvider.notifier)
                            .addOrUpdateSymptoms(selectedSymptoms.toList());
                        Navigator.pop(context);
                      },
                      child: const Text("Save Symptoms",
                          style: TextStyle(color: AppColors.white)),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void showMoodSelectionDialog(BuildContext context, WidgetRef ref) async {
    List<Map<String, String>> moods = [
      {"label": "Neutral", "iconPath": "assets/svg/neutral_mood_icon.svg"},
      {"label": "Sad", "iconPath": "assets/svg/sad_mood_icon.svg"},
      {"label": "Angry", "iconPath": "assets/svg/angry_mood_icon.svg"},
      {"label": "Tired", "iconPath": "assets/svg/tired_mood_icon.svg"},
      {"label": "Sensitive", "iconPath": "assets/svg/sensitive_mood_icon.svg"},
      {"label": "Energetic", "iconPath": "assets/svg/energetic_mood_icon.svg"},
    ];

    String? selectedMood =
        await ref.read(moodProvider.notifier).fetchTodayMood();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              backgroundColor: Theme.of(context).cardColor,
              contentPadding: const EdgeInsets.all(20),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text("How Are You Feeling?",
                            style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            )),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 200,
                    child: Row(
                      children: List.generate(2, (colIndex) {
                        return Expanded(
                          child: Column(
                            children: List.generate(3, (rowIndex) {
                              int index = colIndex * 3 + rowIndex;
                              final mood = moods[index];
                              final isSelected = selectedMood == mood["label"];

                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 6),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      selectedMood = mood["label"];
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10),
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? AppColors.primaryColor
                                          : Theme.of(context).cardColor,
                                      border: Border.all(
                                        color: AppColors.primaryColor,
                                      ),
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                          mood["iconPath"]!,
                                          height: 20,
                                          width: 20,
                                          colorFilter: ColorFilter.mode(
                                            isSelected
                                                ? AppColors.white
                                                : AppColors.primaryColor,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            mood["label"]!,
                                            textAlign: TextAlign.left,
                                            style: TextStyle(
                                              color: isSelected
                                                  ? Colors.white
                                                  : Theme.of(context)
                                                      .textTheme
                                                      .bodyLarge
                                                      ?.color,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ),
                        );
                      }),
                    ),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                        backgroundColor: AppColors.primaryColor,
                      ),
                      onPressed: () {
                        if (selectedMood != null) {
                          ref
                              .read(moodProvider.notifier)
                              .addOrUpdateMood(selectedMood!);
                        }
                        Navigator.pop(context);
                      },
                      child: const Text("Save Mood",
                          style: TextStyle(color: AppColors.white)),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _periodHistory() {
    return Consumer(
      builder: (context, ref, _) {
        final history = ref.watch(recentPeriodHistoryProvider);

        if (history.isEmpty) {
          return Center(child: Text("No period history in the last 3 months."));
        }

        return Column(
          children: history.entries.map((entry) {
            final dateKey = entry.key;
            final readings = entry.value['readings'];

            if (readings == null || readings.isEmpty) return SizedBox.shrink();

            final reading = readings.first;

            final String startRaw =
                reading['phases']['menstrual']['start'] ?? dateKey;
            final String endRaw =
                reading['phases']['menstrual']['end'] ?? 'Unknown';
            final int periodDays = reading['period_duration'] ?? 0;
            final int cycleLength = reading['cycle_length'] ?? 28;
            final double progress = (periodDays / cycleLength).clamp(0.0, 1.0);

            String formatDate(String raw) {
              try {
                final date = DateFormat("dd-MM-yyyy").parse(raw);
                return DateFormat("MMM d").format(date);
              } catch (e) {
                return raw;
              }
            }

            final String formattedStart = formatDate(startRaw);
            final String formattedEnd = formatDate(endRaw);

            return Container(
              margin: EdgeInsets.only(bottom: MySize.size16),
              padding:
                  EdgeInsets.only(left: MySize.size5, right: MySize.size12),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(MySize.size10),
              ),
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: MySize.size12),
                    padding: EdgeInsets.all(MySize.size8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.access_time,
                        color: AppColors.white, size: MySize.size22),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "$formattedStart - $formattedEnd",
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Space.height(6),
                        Text(
                          "$cycleLength Days (Period: $periodDays days)",
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.textGray,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: MySize.size30,
                    width: MySize.size60,
                    alignment: Alignment.center,
                    child: LinearProgressIndicator(
                      value: progress,
                      backgroundColor:
                          AppColors.textGray.withValues(alpha: 0.2),
                      color: AppColors.primaryColor,
                      minHeight: MySize.size8,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        );
      },
    );
  }

  String _monthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  bool _isSameDate(DateTime a, DateTime b) =>
      a.year == b.year && a.month == b.month && a.day == b.day;

  String _weekdayShort(DateTime date) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.weekday % 7];
  }

  Widget buildFlowOption(
    BuildContext context, {
    required String title,
    required int iconCount,
    required bool isSelected,
    required VoidCallback onTap,
    double? fixedWidth, // NEW: allow custom width
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: MySize.size10),
      child: SizedBox(
        width: fixedWidth ?? double.infinity,
        child: OutlinedButton(
          onPressed: onTap,
          style: OutlinedButton.styleFrom(
            backgroundColor: isSelected
                ? AppColors.primaryColor.withValues(alpha: 0.1)
                : AppColors.white100,
            side: BorderSide(
              color: AppColors.white100,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            padding: EdgeInsets.all(MySize.size12),
          ),
          child: Row(
            children: [
              Row(
                children: List.generate(
                  iconCount,
                  (_) => Padding(
                    padding: const EdgeInsets.only(right: 4.0),
                    child: SvgPicture.asset(
                      "assets/svg/flow_icon.svg",
                      width: MySize.size20,
                      height: MySize.size20,
                    ),
                  ),
                ),
              ),
              Space.width(10),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: isSelected ? AppColors.primaryColor : Colors.grey[800],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
