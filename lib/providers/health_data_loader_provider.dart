import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/blood_pressure_provider.dart';
import 'package:healo/providers/bmi_provider.dart';
import 'package:healo/providers/diabetes_provider.dart';
import 'package:healo/providers/fitbit_provider.dart';
import 'package:healo/providers/hba1c_provider.dart';
import 'package:healo/providers/health_provider.dart';
import 'package:healo/providers/kidney_provider.dart';
import 'package:healo/providers/liver_provider.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/providers/period_provider.dart';
import 'package:healo/providers/thyroid_provider.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:healo/providers/water_intake_provider.dart';

/// State for the health data loader
enum HealthDataLoadingState {
  initial,
  loading,
  loaded,
  error,
}

/// Provider state class
class HealthDataLoaderState {
  final HealthDataLoadingState state;
  final String? errorMessage;
  final bool isRefreshing;

  HealthDataLoaderState({
    required this.state,
    this.errorMessage,
    this.isRefreshing = false,
  });

  HealthDataLoaderState copyWith({
    HealthDataLoadingState? state,
    String? errorMessage,
    bool? isRefreshing,
  }) {
    return HealthDataLoaderState(
      state: state ?? this.state,
      errorMessage: errorMessage ?? this.errorMessage,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

/// Provider for loading all health data
final healthDataLoaderProvider = StateNotifierProvider<HealthDataLoaderNotifier, HealthDataLoaderState>(
  (ref) => HealthDataLoaderNotifier(ref),
);

/// Notifier for loading all health data
class HealthDataLoaderNotifier extends StateNotifier<HealthDataLoaderState> {
  final Ref _ref;

  HealthDataLoaderNotifier(this._ref) : super(HealthDataLoaderState(state: HealthDataLoadingState.initial));

  /// Load all health data in parallel
  Future<void> loadAllHealthData() async {
    if (state.state == HealthDataLoadingState.loading) return;

    state = state.copyWith(state: HealthDataLoadingState.loading);

    try {
      log('Starting to load all health data');

      // Create a list of futures for all data loading operations
      final futures = <Future>[];

      // BMI and weight data
      futures.add(_ref.read(bmiProvider.notifier).fetchBMI());
      futures.add(_ref.read(weightProvider.notifier).fetchWeight());
      futures.add(_ref.read(heightProvider.notifier).fetchHeight());
      futures.add(_ref.read(latestBMIProvider.notifier).fetchLatestBMI());
      futures.add(_ref.read(bmiHistoryProvider.notifier).fetchBMIHistory());
      futures.add(_ref.read(bmiMonthlyHistoryProvider.notifier).fetchBMIMonthlyHistory());

      // Refresh user data to ensure combined providers have latest profile data
      _ref.invalidate(userDataProvider);
      _ref.invalidate(userDataStreamProvider);

      // Invalidate combined BMI providers to ensure they use latest data
      _ref.invalidate(combinedBmiProvider);
      _ref.invalidate(combinedHeightProvider);
      _ref.invalidate(combinedWeightProvider);

      // Blood pressure data
      futures.add(_ref.read(bloodPressureHistoryProvider.notifier).fetchBloodPressureHistory());

      // Diabetes data
      futures.add(_ref.read(latestSugarReadingProvider.notifier).fetchLatestReading());
      futures.add(_ref.read(estimatedHba1cProvider.notifier).fetchEstimatedHba1c());

      // HbA1c data
      futures.add(_ref.read(latestHba1cReadingProvider.notifier).fetchLatestReading());
      futures.add(_ref.read(latestHba1cDateProvider.notifier).fetchLatestDate());

      // Kidney data
      futures.add(_ref.read(kidneyHistoryProvider.notifier).fetchKidneyHistory());

      // Thyroid data
      futures.add(_ref.read(thyroidHistoryProvider.notifier).fetchThyroidHistory());

      // Liver data
      futures.add(_ref.read(liverHistoryProvider.notifier).fetchLiverHistory());

      // Water intake data
      futures.add(_ref.read(updatedTodayProvider.notifier).updatedToday());
      futures.add(_ref.read(glassesTodayProvider.notifier).fetchGlassesToday());

      // Period data (if applicable)
      futures.add(_ref.read(periodProvider.notifier).fetchPeriodHistory());

      // Medication data
      futures.add(_ref.read(medicationProvider.notifier).fetchDailyMedicationHistory());
      futures.add(_ref.read(weeklyMedicationProvider.notifier).fetchWeeklyMedicationHistory());
      futures.add(_ref.read(monthlyMedicationProvider.notifier).fetchMonthlyMedicationHistory());

      // FitBit data initialization (non-blocking)
      futures.add(_ref.read(fitbitProvider.notifier).fetchTodayData().catchError((e) {
        log('FitBit data fetch failed (non-critical): $e');
      }));

      // Health data from device and Firestore
      futures.add(_ref.read(healthDataProvider.notifier).initializeFromFirestore().then((_) {
        return _ref.read(healthDataProvider.notifier).refreshHealthData();
      }));

      // Wait for all futures to complete
      await Future.wait(futures);

      log('All health data loaded successfully');
      state = state.copyWith(state: HealthDataLoadingState.loaded);
    } catch (e) {
      log('Error loading health data: $e');
      state = state.copyWith(
        state: HealthDataLoadingState.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// Refresh all health data
  Future<void> refreshAllHealthData() async {
    state = state.copyWith(isRefreshing: true);

    try {
      await loadAllHealthData();
    } finally {
      state = state.copyWith(isRefreshing: false);
    }
  }
}
