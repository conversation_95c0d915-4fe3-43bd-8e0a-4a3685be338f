import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:intl/intl.dart';

class MeasurementsState {
  final double height;
  final String heightUnit;
  final double weight;
  final String weightUnit;
  final double bmi;

  MeasurementsState({
    required this.height,
    required this.heightUnit,
    required this.weight,
    required this.weightUnit,
    required this.bmi,
  });

  MeasurementsState copyWith({
    double? height,
    String? heightUnit,
    double? weight,
    String? weightUnit,
    double? bmi,
  }) {
    return MeasurementsState(
      height: height ?? this.height,
      heightUnit: heightUnit ?? this.heightUnit,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
      bmi: bmi ?? this.bmi,
    );
  }
}

class MeasurementsNotifier extends StateNotifier<MeasurementsState> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  MeasurementsNotifier(this._firestoreService, this._ref)
      : super(MeasurementsState(
          height: 170.0,
          heightUnit: 'cm',
          weight: 65.0,
          weightUnit: 'kg',
          bmi: 0.0,
        )) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in - could fetch latest measurements here if needed
          } else {
            // User logged out, reset to default values
            state = MeasurementsState(
              height: 170.0,
              heightUnit: 'cm',
              weight: 65.0,
              weightUnit: 'kg',
              bmi: 0.0,
            );
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in measurements provider: $error");
        },
      );
    });
  }

  Future<void> saveMeasurements({
    required double height,
    required String heightUnit,
    required double weight,
    required String weightUnit,
  }) async {
    double heightInMeters = heightUnit == 'cm' ? height / 100 : height * 0.0254;

    double weightInKg = weightUnit == 'kg' ? weight : weight * 0.453592;

    double bmi = weightInKg / (heightInMeters * heightInMeters);

    state = state.copyWith(
      height: height,
      heightUnit: heightUnit,
      weight: weight,
      weightUnit: weightUnit,
      bmi: bmi,
    );

    await _firestoreService.updateBMI(
        bmi: bmi,
        weight: weight,
        weightUnit: weightUnit,
        height: height,
        heightUnit: heightUnit);
  }
}

final measurementsProvider =
    StateNotifierProvider<MeasurementsNotifier, MeasurementsState>(
        (ref) => MeasurementsNotifier(FirestoreService(), ref));

final bmiHistoryProvider = StateNotifierProvider<BMIHistoryNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => BMIHistoryNotifier(FirestoreService(), ref),
);

class BMIHistoryNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  BMIHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchBMIHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in BMI history provider: $error");
        },
      );
    });
  }

  Future<void> fetchBMIHistory() async {
    try {
      final data = await _firestoreService.fetchBMIHistory();
      final bmiHistory = Map<String, dynamic>.from(data['history'] ?? {});

      final Map<String, Map<String, dynamic>> formattedData = {};
      log("Bmi History: ${bmiHistory.length}");
      for (final entry in bmiHistory.entries) {
        final dateKey = entry.key;
        final dayData = entry.value;

        if (dayData != null && dayData is Map<String, dynamic>) {
          formattedData[dateKey] = {
            'day': dayData['day'],
            'bmi': dayData['bmi'],
          };
        }
      }
      state = formattedData;
      log(formattedData.toString());
    } catch (e) {
      log("Error fetching BMI history: $e");
      state = {};
    }
  }
}

final bmiMonthlyHistoryProvider = StateNotifierProvider<
    BMIMonthlyHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => BMIMonthlyHistoryNotifier(FirestoreService(), ref),
);

class BMIMonthlyHistoryNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  BMIMonthlyHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchBMIMonthlyHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in BMI monthly history provider: $error");
        },
      );
    });
  }

  Future<void> fetchBMIMonthlyHistory() async {
    try {
      final data = await _firestoreService.fetchBMIHistory();
      final bmiHistory = Map<String, dynamic>.from(data['history'] ?? {});

      final Map<String, Map<String, dynamic>> formattedData = {};

      for (final entry in bmiHistory.entries) {
        final dateKey = entry.key;
        final dayData = entry.value;

        if (dayData != null && dayData is Map<String, dynamic>) {
          formattedData[dateKey] = {
            'day': dayData['day'],
            'bmi': dayData['bmi'],
          };
        }
      }
      state = formattedData;
    } catch (e) {
      log("Error fetching BMI Monthly history: $e");
      state = {};
    }
  }
}

final bmiProvider = StateNotifierProvider<BMINotifier, double>(
  (ref) => BMINotifier(FirestoreService(), ref),
);

// Provider for latest BMI data with date information
class LatestBMIData {
  final double bmi;
  final String date;
  final String time;
  final double weight;
  final double height;
  final String weightUnit;
  final String heightUnit;

  LatestBMIData({
    required this.bmi,
    required this.date,
    required this.time,
    required this.weight,
    required this.height,
    required this.weightUnit,
    required this.heightUnit,
  });
}

class LatestBMINotifier extends StateNotifier<LatestBMIData?> {
  final FirestoreService _firestoreService;

  LatestBMINotifier(this._firestoreService) : super(null);

  Future<void> fetchLatestBMI() async {
    try {
      final data = await _firestoreService.fetchBMIHistory();
      final bmiHistory = Map<String, dynamic>.from(data['history'] ?? {});

      if (bmiHistory.isNotEmpty) {
        final sortedKeys = bmiHistory.keys.toList()
          ..sort((a, b) {
            final dateA = DateFormat('d-M-yyyy').parse(a);
            final dateB = DateFormat('d-M-yyyy').parse(b);
            return dateA.compareTo(dateB);
          });

        final lastKey = sortedKeys.last;
        final lastEntry = Map<String, dynamic>.from(bmiHistory[lastKey]);

        state = LatestBMIData(
          bmi: (lastEntry['bmi'] as num).toDouble(),
          date: lastKey,
          time: lastEntry['time'] ?? 'Unknown',
          weight: (lastEntry['weight'] as num).toDouble(),
          height: (lastEntry['height'] as num).toDouble(),
          weightUnit: lastEntry['weight_unit'] ?? 'kg',
          heightUnit: lastEntry['height_unit'] ?? 'cm',
        );
      } else {
        state = null;
      }
    } catch (e) {
      log("Error fetching latest BMI: $e");
      state = null;
    }
  }
}

final latestBMIProvider = StateNotifierProvider<LatestBMINotifier, LatestBMIData?>(
  (ref) => LatestBMINotifier(FirestoreService()),
);

class BMINotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  BMINotifier(this._firestoreService, this._ref) : super(0.0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchBMI();
          } else {
            // User logged out, clear data
            state = 0.0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in BMI provider: $error");
        },
      );
    });
  }

  Future<void> fetchBMI() async {
    try {
      final data = await _firestoreService.fetchBMIHistory();
      final bmiHistory = Map<String, dynamic>.from(data['history'] ?? {});

      if (bmiHistory.isNotEmpty) {
        final sortedKeys = bmiHistory.keys.toList()
          ..sort((a, b) {
            final dateA = DateFormat('d-M-yyyy').parse(a);
            final dateB = DateFormat('d-M-yyyy').parse(b);
            return dateA.compareTo(dateB);
          });

        final lastKey = sortedKeys.last;
        final lastEntry = Map<String, dynamic>.from(bmiHistory[lastKey]);

        final double lastBMI = (lastEntry['bmi'] as num).toDouble();

        state = lastBMI;
      } else {
        state = 0.0;
      }
    } catch (e) {
      log("Error fetching BMI: $e");
      state = 0.0;
    }
  }
}

final weightProvider = StateNotifierProvider<WeightNotifier, double>(
  (ref) => WeightNotifier(FirestoreService(), ref),
);

class WeightNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  WeightNotifier(this._firestoreService, this._ref) : super(0.0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchWeight();
          } else {
            // User logged out, clear data
            state = 0.0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in weight provider: $error");
        },
      );
    });
  }

  Future<void> fetchWeight() async {
    try {
      final data = await _firestoreService.fetchBMIHistory();
      final bmiHistory = Map<String, dynamic>.from(data['history'] ?? {});

      if (bmiHistory.isNotEmpty) {
        final sortedKeys = bmiHistory.keys.toList()
          ..sort((a, b) {
            final dateA = DateFormat('d-M-yyyy').parse(a);
            final dateB = DateFormat('d-M-yyyy').parse(b);
            return dateA.compareTo(dateB);
          });

        final lastKey = sortedKeys.last;
        final lastEntry = Map<String, dynamic>.from(bmiHistory[lastKey]);

        final double lastWeight = (lastEntry['weight'] as num).toDouble();

        state = lastWeight;
      } else {
        state = 0.0;
      }
    } catch (e) {
      log("Error fetching weight: $e");
      state = 0.0;
    }
  }
}

final heightProvider = StateNotifierProvider<HeightNotifier, double>(
  (ref) => HeightNotifier(FirestoreService(), ref),
);

// Combined BMI provider that prioritizes BMI collection data over user profile data
final combinedBmiProvider = Provider<double>((ref) {
  final historicalBmi = ref.watch(bmiProvider);
  final userHeight = ref.watch(userHeightProvider);
  final userWeight = ref.watch(userWeightProvider);

  // First priority: Use BMI collection data if available
  if (historicalBmi > 0) {
    return historicalBmi;
  }

  // Second priority: Calculate from user profile data if available
  if (userHeight != null && userWeight != null && userHeight > 0 && userWeight > 0) {
    // Convert height from cm to meters
    final heightInMeters = userHeight / 100;
    // Calculate BMI
    final calculatedBmi = userWeight / (heightInMeters * heightInMeters);
    return calculatedBmi.toDouble();
  }

  // Default: return 0 if no data available
  return 0.0;
});

// Combined height provider that prioritizes BMI collection data over user profile data
final combinedHeightProvider = Provider<double>((ref) {
  final historicalHeight = ref.watch(heightProvider);
  final userHeight = ref.watch(userHeightProvider);

  // First priority: Use BMI collection data if available
  if (historicalHeight > 0) {
    return historicalHeight;
  }

  // Second priority: Use user profile data if available
  if (userHeight != null && userHeight > 0) {
    return userHeight.toDouble();
  }

  // Default: return 0 if no data available
  return 0.0;
});

// Combined weight provider that prioritizes BMI collection data over user profile data
final combinedWeightProvider = Provider<double>((ref) {
  final historicalWeight = ref.watch(weightProvider);
  final userWeight = ref.watch(userWeightProvider);

  // First priority: Use BMI collection data if available
  if (historicalWeight > 0) {
    return historicalWeight;
  }

  // Second priority: Use user profile data if available
  if (userWeight != null && userWeight > 0) {
    return userWeight.toDouble();
  }

  // Default: return 0 if no data available
  return 0.0;
});

class HeightNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  HeightNotifier(this._firestoreService, this._ref) : super(0.0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchHeight();
          } else {
            // User logged out, clear data
            state = 0.0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in height provider: $error");
        },
      );
    });
  }

  Future<void> fetchHeight() async {
    try {
      final data = await _firestoreService.fetchBMIHistory();
      final bmiHistory = Map<String, dynamic>.from(data['history'] ?? {});

      if (bmiHistory.isNotEmpty) {
        final sortedKeys = bmiHistory.keys.toList()
          ..sort((a, b) {
            final dateA = DateFormat('d-M-yyyy').parse(a);
            final dateB = DateFormat('d-M-yyyy').parse(b);
            return dateA.compareTo(dateB);
          });

        final lastKey = sortedKeys.last;
        final lastEntry = Map<String, dynamic>.from(bmiHistory[lastKey]);

        final double lastHeight = (lastEntry['height'] as num).toDouble();

        state = lastHeight;
      } else {
        state = 0.0;
      }
    } catch (e) {
      log("Error fetching height: $e");
      state = 0.0;
    }
  }
}

final bmiFullHistoryProvider = StateNotifierProvider<BMIFullHistoryNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => BMIFullHistoryNotifier(FirestoreService(), ref),
);

class BMIFullHistoryNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  BMIFullHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchBMIFullHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in BMI full history provider: $error");
        },
      );
    });
  }

  Future<void> fetchBMIFullHistory() async {
    try {
      final data = await _firestoreService.fetchBMIHistory();
      final bmiHistory = Map<String, dynamic>.from(data['history'] ?? {});
      final Map<String, Map<String, dynamic>> formattedData = {};

      bmiHistory.forEach((dateKey, dayData) {
        formattedData[dateKey] = {
          'weight': dayData['weight'],
          'height': dayData['height'],
          'time': dayData['time'],
          'height_unit': dayData['height_unit'],
          'weight_unit': dayData['weight_unit']
        };
      });

      state = formattedData;
    } catch (e) {
      log("Error fetching BMI full history: $e");
      state = {};
    }
  }
}
