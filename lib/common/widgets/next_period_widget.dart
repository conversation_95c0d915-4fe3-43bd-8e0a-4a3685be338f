import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/providers/period_provider.dart';

class NextPeriodWidget extends ConsumerStatefulWidget {
  const NextPeriodWidget({super.key});

  @override
  ConsumerState<NextPeriodWidget> createState() => _NextPeriodWidgetState();
}

class _NextPeriodWidgetState extends ConsumerState<NextPeriodWidget> {
  @override
  void initState() {
    super.initState();
    // Initialize the next period prediction when widget loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(nextPeriodPredictionProvider.notifier).calculateNextPeriod();
    });
  }

  @override
  Widget build(BuildContext context) {
    final nextPeriodState = ref.watch(nextPeriodPredictionProvider);

    String title = 'Next Period';
    String subtitle = 'Loading...';
    Color iconColor = AppColors.primaryColor;
    IconData iconData = Icons.access_time;

    if (nextPeriodState != null) {
      title = nextPeriodState.title;
      subtitle = nextPeriodState.subtitle;

      // Set different colors and icons based on period status
      if (nextPeriodState.isEarlyPeriod) {
        iconColor = Colors.blue;
        iconData = Icons.fast_rewind;
      } else if (nextPeriodState.isLatePeriod) {
        iconColor = Colors.orange;
        iconData = Icons.fast_forward;
      } else if (nextPeriodState.isOverdue) {
        iconColor = Colors.red;
        iconData = Icons.warning;
      } else if (nextPeriodState.hasInsufficientData) {
        iconColor = AppColors.textGray;
        iconData = Icons.info;
      } else {
        iconColor = AppColors.primaryColor;
        iconData = Icons.access_time;
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size15),
      ),
      child: Padding(
        padding: EdgeInsets.all(MySize.size15),
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MySize.size8),
              padding: EdgeInsets.all(MySize.size8),
              decoration: BoxDecoration(
                color: iconColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                iconData,
                color: AppColors.white,
                size: MySize.size22,
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: AppColors.textGray,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
