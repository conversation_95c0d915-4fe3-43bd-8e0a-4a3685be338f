import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/providers/period_provider.dart';

class NextPeriodWidget extends ConsumerStatefulWidget {
  const NextPeriodWidget({super.key});

  @override
  ConsumerState<NextPeriodWidget> createState() => _NextPeriodWidgetState();
}

class _NextPeriodWidgetState extends ConsumerState<NextPeriodWidget> {
  @override
  void initState() {
    super.initState();
    // Initialize the next period prediction when widget loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(nextPeriodPredictionProvider.notifier).calculateNextPeriod();
    });
  }

  @override
  Widget build(BuildContext context) {
    final nextPeriodState = ref.watch(nextPeriodPredictionProvider);

    String title = 'Next Period';
    String subtitle = 'Loading...';

    if (nextPeriodState != null) {
      title = nextPeriodState.title;
      subtitle = nextPeriodState.subtitle;
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size15),
      ),
      child: Padding(
        padding: EdgeInsets.all(MySize.size15),
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MySize.size8),
              padding: EdgeInsets.all(MySize.size8),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.access_time,
                color: AppColors.white,
                size: MySize.size22,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textGray,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
