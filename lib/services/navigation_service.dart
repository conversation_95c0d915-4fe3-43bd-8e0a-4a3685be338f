import 'package:flutter/material.dart';
import 'package:healo/route/route_constants.dart';
import 'dart:developer';

/// Service class to handle navigation from background notifications
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Navigate to medication screen
  static Future<void> navigateToMedicationScreen() async {
    try {
      final context = navigatorKey.currentContext;
      if (context != null) {
        log('Navigating to medication screen from notification');
        Navigator.of(context).pushNamed(medicationListScreen);
      } else {
        log('Navigation context is null, cannot navigate');
      }
    } catch (e) {
      log('Error navigating to medication screen: $e');
    }
  }

  /// Navigate to medication screen with medication details for dialog
  static Future<void> navigateToMedicationScreenWithDetails({
    required String medicationName,
    required String time,
    required String dosage,
    String? notificationId,
    bool appWasAlreadyOpen = false,
  }) async {
    try {
      final context = navigatorKey.currentContext;
      if (context != null) {
        log('Navigating to medication screen with details: $medicationName, $time, $dosage');
        log('Notification ID: $notificationId');
        log('App was already open: $appWasAlreadyOpen');

        // Create medication details map
        final medicationDetails = {
          'medication_name': medicationName,
          'time': time,
          'dosage': dosage,
          'notification_id': notificationId,
          'from_notification': true,
          'app_was_already_open': appWasAlreadyOpen,
        };

        Navigator.of(context).pushNamed(medicationListScreen, arguments: medicationDetails);
      } else {
        log('Navigation context is null, cannot navigate');
      }
    } catch (e) {
      log('Error navigating to medication screen with details: $e');
    }
  }

  /// Navigate to specific screen by route name
  static Future<void> navigateToScreen(String routeName, {Object? arguments}) async {
    try {
      final context = navigatorKey.currentContext;
      if (context != null) {
        log('Navigating to screen: $routeName');
        Navigator.of(context).pushNamed(routeName, arguments: arguments);
      } else {
        log('Navigation context is null, cannot navigate to $routeName');
      }
    } catch (e) {
      log('Error navigating to screen $routeName: $e');
    }
  }

  /// Navigate to main screen and then to medication screen
  static Future<void> navigateToMainThenMedication() async {
    try {
      final context = navigatorKey.currentContext;
      if (context != null) {
        log('Navigating to main screen then medication screen');
        // First navigate to main screen
        Navigator.of(context).pushNamedAndRemoveUntil(
          mainScreen,
          (route) => false,
        );
        // Then navigate to medication screen after delay
        Future.delayed(const Duration(milliseconds: 500), () {
          final newContext = navigatorKey.currentContext;
          if (newContext != null) {
            Navigator.of(newContext).pushNamed(medicationListScreen);
          }
        });
      } else {
        log('Navigation context is null, cannot navigate');
      }
    } catch (e) {
      log('Error navigating to main then medication screen: $e');
    }
  }

  /// Get current context
  static BuildContext? get currentContext => navigatorKey.currentContext;

  /// Check if navigator is ready
  static bool get isNavigatorReady => navigatorKey.currentContext != null;
}
